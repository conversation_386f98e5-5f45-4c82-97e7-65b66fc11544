#!/usr/bin/env python3
"""
Check if there are more recent crawl IDs for the problematic URLs.
This script will look for any recent crawls that might not be in our cache.
"""

import os
import json
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the backend directory to the Python path
sys.path.append('backend')

def check_recent_crawls():
    """Check for recent crawls that might not be in our cache."""
    
    print("🔍 CHECKING FOR RECENT CRAWLS")
    print("=" * 50)
    
    # Check if Firecrawl API key is available
    firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
    if not firecrawl_api_key:
        print("❌ FIRECRAWL_API_KEY not found in environment")
        return
    
    # Initialize Firecrawl
    try:
        from firecrawl import FirecrawlApp
        firecrawl = FirecrawlApp(api_key=firecrawl_api_key)
        print("✅ Firecrawl client initialized")
    except Exception as e:
        print(f"❌ Error initializing Firecrawl: {e}")
        return
    
    # Load current cache
    cache_file = 'data/firecrawl_cache.json'
    cache_data = {}
    if os.path.exists(cache_file):
        with open(cache_file, 'r', encoding='utf-8') as f:
            cache_data = json.load(f)
        print(f"✅ Current cache loaded with {len(cache_data)} entries")
    
    # Test URLs that are showing 0 jobs
    test_urls = [
        "https://www.hapeko.de/bewerbende/stellenangebote/p1",
        "https://www.psp.de/stellenausschreibungen.html"
    ]
    
    print(f"\n🔍 Checking for active crawls...")
    
    # Check for active crawls
    try:
        # Note: This might not be available in all Firecrawl versions
        # We'll try to get active crawls if the method exists
        print("   Attempting to get active crawls...")
        
        # Let's try a different approach - check if we can get recent crawl history
        # Since we can't directly get active crawls, let's see what we can do
        
        for url in test_urls:
            print(f"\n🔍 Checking {url}")
            
            # Check current cache entry
            if url in cache_data:
                cached_info = cache_data[url]
                cached_id = cached_info.get('crawl_id')
                cached_time = cached_info.get('timestamp')
                print(f"   📋 Cached: {cached_id[:8]}... from {cached_time}")
                
                # Try to check if this crawl is still accessible
                try:
                    status = firecrawl.check_crawl_status(cached_id)
                    if status:
                        print(f"   ✅ Cached crawl still accessible: {status.status}")
                        if hasattr(status, 'data') and status.data:
                            print(f"   📄 Pages available: {len(status.data)}")
                        else:
                            print(f"   ❌ No data in cached crawl")
                    else:
                        print(f"   ❌ Cached crawl not accessible")
                except Exception as e:
                    print(f"   ❌ Error checking cached crawl: {e}")
            else:
                print(f"   ❌ No cache entry found")
            
            # Try to start a new crawl to get a fresh crawl ID
            print(f"   🚀 Testing new crawl for {url}...")
            try:
                # Start a small test crawl
                crawl_result = firecrawl.async_crawl_url(
                    url,
                    limit=2,  # Very small limit for testing
                    scrape_options={
                        'formats': ['markdown'],
                        'only_main_content': True
                    }
                )
                
                if crawl_result and crawl_result.success:
                    new_crawl_id = crawl_result.id
                    print(f"   ✅ New crawl started: {new_crawl_id}")
                    
                    # Wait a moment and check status
                    import time
                    time.sleep(5)
                    
                    new_status = firecrawl.check_crawl_status(new_crawl_id)
                    if new_status:
                        print(f"   📊 New crawl status: {new_status.status}")
                        if new_status.status == 'completed' and new_status.data:
                            print(f"   🎉 New crawl completed with {len(new_status.data)} pages!")
                            
                            # Update cache with new crawl ID
                            cache_data[url] = {
                                'crawl_id': new_crawl_id,
                                'timestamp': datetime.now().isoformat()
                            }
                            
                            # Save updated cache
                            with open(cache_file, 'w', encoding='utf-8') as f:
                                json.dump(cache_data, f, indent=2, ensure_ascii=False)
                            print(f"   💾 Cache updated with new crawl ID")
                        else:
                            print(f"   ⏳ New crawl still in progress...")
                    else:
                        print(f"   ❌ Could not get new crawl status")
                else:
                    print(f"   ❌ Failed to start new crawl")
                    
            except Exception as e:
                print(f"   ❌ Error starting new crawl: {e}")
    
    except Exception as e:
        print(f"❌ Error checking for recent crawls: {e}")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"   1. The cached crawl IDs have expired on Firecrawl servers")
    print(f"   2. You need to run a fresh scrape to get new crawl IDs")
    print(f"   3. The 'Reload from Firecrawl' button only works with recent crawls")
    print(f"   4. Consider running scrapes more frequently to avoid expiration")

if __name__ == "__main__":
    check_recent_crawls()
