@echo off
title Jomade MVP - Executive Search Application

echo ========================================
echo    Starting Jomade MVP Application
echo ========================================
echo.

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"
echo Script directory: %SCRIPT_DIR%

REM Change to the script directory
cd /d "%SCRIPT_DIR%"
echo Current directory: %CD%
echo.

REM Check if we're in the right directory by looking for run.ps1
if not exist "run.ps1" (
    echo ERROR: run.ps1 not found in %CD%
    echo Expected path: %CD%\run.ps1
    echo.
    echo Please ensure this batch file is in the Jomade MVP root directory
    echo Directory contents:
    dir /b
    echo.
    pause
    exit
)

echo Found run.ps1 - using the official launcher
echo.
echo The application will start with:
echo   - Backend server: http://localhost:8000
echo   - Frontend server: http://localhost:3000
echo.
echo Starting PowerShell to run the application...
echo.

REM Run the official PowerShell script
powershell -ExecutionPolicy Bypass -File "run.ps1"

echo.
echo Application stopped.
pause
