#!/usr/bin/env python3
"""
Debug the cache freshness logic to understand why _get_cached_crawl_id returns None.
"""

import os
import json
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the backend directory to the Python path
sys.path.append('backend')

def debug_cache_freshness():
    """Debug the cache freshness logic."""
    
    print("🔍 DEBUG: Cache Freshness Logic")
    print("=" * 50)
    
    # Load cache data
    cache_file = 'data/firecrawl_cache.json'
    if not os.path.exists(cache_file):
        print(f"❌ Cache file {cache_file} not found")
        return
    
    with open(cache_file, 'r', encoding='utf-8') as f:
        cache_data = json.load(f)
    
    print(f"✅ Cache loaded with {len(cache_data)} entries")
    
    # Get cache hours from environment
    cache_hours = int(os.getenv('FIRECRAWL_CACHE_HOURS', '24'))
    print(f"✅ Cache hours setting: {cache_hours}")
    
    # Test URLs
    test_urls = [
        "https://www.hapeko.de/bewerbende/stellenangebote/p1",
        "https://www.psp.de/stellenausschreibungen.html"
    ]
    
    now = datetime.now()
    print(f"✅ Current time: {now.isoformat()}")
    
    for url in test_urls:
        if url not in cache_data:
            print(f"❌ {url} not in cache")
            continue
            
        crawl_info = cache_data[url]
        crawl_id = crawl_info.get('crawl_id')
        timestamp_str = crawl_info.get('timestamp')
        
        print(f"\n🔍 {url}")
        print(f"   🆔 Crawl ID: {crawl_id}")
        print(f"   📅 Cached timestamp: {timestamp_str}")
        
        # Parse timestamp and check freshness
        try:
            cached_time = datetime.fromisoformat(timestamp_str)
            time_diff = now - cached_time
            age_hours = time_diff.total_seconds() / 3600
            
            print(f"   ⏰ Cache age: {age_hours:.2f} hours")
            print(f"   📏 Cache limit: {cache_hours} hours")
            
            is_fresh = time_diff < timedelta(hours=cache_hours)
            print(f"   ✅ Is fresh: {is_fresh}")
            
            if is_fresh:
                print(f"   ✅ Should return crawl ID: {crawl_id}")
            else:
                print(f"   ❌ Cache expired, would return None")
                print(f"   💡 Cache expired {age_hours - cache_hours:.2f} hours ago")
                
        except Exception as e:
            print(f"   ❌ Error parsing timestamp: {e}")
    
    # Test the actual scraper method
    print(f"\n🔧 Testing actual scraper _get_cached_crawl_id method...")
    try:
        from scraper import create_scraper
        scraper = create_scraper(timeout_seconds=300, cache_hours=cache_hours)
        if scraper:
            for url in test_urls:
                if url in cache_data:
                    cached_id = scraper._get_cached_crawl_id(url)
                    print(f"   {url}: {cached_id}")
        else:
            print("   ❌ Failed to create scraper")
    except Exception as e:
        print(f"   ❌ Error testing scraper: {e}")

if __name__ == "__main__":
    debug_cache_freshness()
