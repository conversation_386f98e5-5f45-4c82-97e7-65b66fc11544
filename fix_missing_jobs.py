#!/usr/bin/env python3
"""
Fix missing jobs by running the scraper for URLs that are cached but missing from database.
"""

import os
import json
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend to path
sys.path.append('backend')

def fix_missing_jobs():
    """Fix missing jobs by running scraper and saving results."""
    
    print("=" * 80)
    print("🔧 FIXING MISSING JOBS FROM SCRAPED URLS")
    print("=" * 80)
    
    # URLs that are scraped but missing jobs
    missing_urls = [
        {
            "url": "https://www.bollmann-executives.de/ihre-karriere/stellenangebote",
            "prefix": "AAW",
            "name": "bollmann-executives"
        },
        {
            "url": "https://delphi-hr.de/jobs/",
            "prefix": "AAZ", 
            "name": "delphi-hr"
        },
        {
            "url": "https://www.qrc-group.com/jobs/",
            "prefix": "ABR",
            "name": "qrc-group"
        }
    ]
    
    try:
        from scraper import JobScraper
        from storage import JobStore
        
        # Create instances
        scraper = JobScraper()
        storage = JobStore('data/jobs.json')
        
        print("✅ Scraper and storage instances created")
        
        total_jobs_added = 0
        
        for url_info in missing_urls:
            url = url_info["url"]
            prefix = url_info["prefix"]
            name = url_info["name"]
            
            print(f"\n🔧 PROCESSING: {name} ({prefix})")
            print(f"📍 URL: {url}")
            
            # Check current jobs for this source
            current_jobs = [job for job in storage.data if job.get('source') == prefix]
            print(f"📊 Current jobs in database: {len(current_jobs)}")
            
            # Run scraper
            print(f"🔍 Running scraper...")
            jobs = scraper.crawl_url(url, prefix, force_scrape=False)
            
            print(f"📊 Scraper extracted: {len(jobs)} jobs")
            
            if jobs:
                print(f"💾 Saving jobs to database...")
                jobs_before = len(storage.data)
                
                # Add jobs to storage
                for job in jobs:
                    storage.add(job)
                
                jobs_after = len(storage.data)
                jobs_added = jobs_after - jobs_before
                total_jobs_added += jobs_added
                
                print(f"✅ Added {jobs_added} jobs to database")
                
                # Show sample jobs
                if jobs_added > 0:
                    print(f"📋 Sample jobs added:")
                    sample_jobs = [job for job in storage.data if job.get('source') == prefix][-min(3, jobs_added):]
                    for job in sample_jobs:
                        print(f"   - {job['id']}: {job['title'][:50]}...")
            else:
                print(f"❌ No jobs extracted")
        
        print(f"\n" + "="*80)
        print(f"🎯 SUMMARY")
        print(f"="*80)
        print(f"📊 Total jobs added: {total_jobs_added}")
        print(f"📊 Total jobs in database: {len(storage.data)}")
        
        # Show final counts by source
        for url_info in missing_urls:
            prefix = url_info["prefix"]
            name = url_info["name"]
            jobs_for_source = [job for job in storage.data if job.get('source') == prefix]
            print(f"   {name} ({prefix}): {len(jobs_for_source)} jobs")
        
        if total_jobs_added > 0:
            print(f"\n✅ SUCCESS: Fixed missing jobs! Check the frontend to see the new listings.")
        else:
            print(f"\n⚠️  No new jobs were added. The URLs may not have extractable job content.")
            
    except Exception as e:
        print(f"❌ Error fixing missing jobs: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "=" * 80)
    print("🏁 FIX COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    fix_missing_jobs()
