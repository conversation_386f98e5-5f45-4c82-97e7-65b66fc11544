#!/usr/bin/env python3
"""
Test script to analyze PSP.de job extraction using the actual scraped content.
This will help identify why jobs are not being extracted from PSP.de.
"""

import os
import json
import logging
from datetime import datetime
from dotenv import load_dotenv
import openai

# Load environment variables
load_dotenv()

# Sample PSP.de content (from the user's file)
PSP_SAMPLE_CONTENT = """---
url: "https://www.psp.de/stellenausschreibungen.html?stelle=1060"
title: "PSP | Stellenausschreibungen | Bereichsleiter Logistik & Supply Chain Management (m/w/d) | COO/Geschäftsführer in spe "
---

# Stellenausschreibung

[Alle Stellenausschreibungen](https://www.psp.de/stellenausschreibungen.html)

Kennziffer **50713**

## Bereichsleiter Logistik & Supply Chain Management (m/w/d)  COO/Geschäftsführer in spe

Standort: **Hessen**

Branche: **B2B Großhandel im C-Teile Bereich**

**Ihre Chance auf eine echte Perspektive**

Sie starten als Bereichsleiter für Logistik und Supply Chain Management und bringen die erforderliche Expertise mit, um bestehende logistische Prozesse zu analysieren, zu optimieren und nachhaltige Logistikstrategien zu entwickeln. Ziel ist es, hohe Kosteneffizienz mit bestmöglicher Servicequalität zu vereinen. Sie übernehmen die Führung von rund 70 Mitarbeitern und perspektivisch ist Ihr nächster Schritt die Berufung zum COO – mit Verantwortung über weitere zentrale Unternehmensbereiche.

Was unseren Auftraggeber besonders macht: flache Hierarchien, unternehmerisches Denken auf allen Ebenen und ein starker operativer Fokus im Management. Ein Unternehmen, das auf gesunden wirtschaftlichen Fundamenten steht – nachhaltig profitabel.

**Unser Mandant – Marktführer mit klarer Vision**

Unser Auftraggeber ist ein **führendes deutsches Großhandelsunternehmen** für Verpackungslösungen und Systemdienstleistungen im C-Teile-Segment.

Kernprodukte: Kartonagen, Sekundärverpackungen, ergänzende Materialien und Services.

Kunden: breit gefächert – aus nahezu allen Branchen.

**Fakten zum Unternehmen:**

- Top 3 in Deutschland
- Bundesweite Präsenz mit 14 Standorten
- Zentrale in Hessen
- Rund 400 Mitarbeitende
- Jahresumsatz: ca. 170 Mio. Euro
- Logistik als strategischer Erfolgsfaktor:
  - Modernes Logistikzentrum + dezentrale Lagerstrukturen
  - 60.000 Palettenstellplätze
  - Eigene LKW-Flotte mit 50 Fahrzeugen

**Ihre Aufgaben – operativ stark, strategisch vorausschauend**

Im ersten Schritt sorgen Sie für effizientere Prozesse und entwickeln zukunftsfähige Distributionslösungen unter Berücksichtigung globaler Trends und digitaler Möglichkeiten. Unterstützt werden Sie am Hauptstandort von 70 Mitarbeitern und 3 erfahrenen Führungskräften, die Sie im Tagesgeschäft entlasten und für die Sie die Verantwortung übernehmen.

**Was Sie mitbringen sollten**

- Ausbildung oder Studium mit logistischem oder betriebswirtschaftlichem Schwerpunkt
- Mehrjährige Führungserfahrung im Bereich Lager/Logistik, idealerweise im Großhandel
- Alternativ: Erfahrung als Standort- oder Niederlassungsleiter in der Kontraktlogistik
- Ausgeprägter Gestaltungswille, Umsetzungsstärke und Hands-on-Mentalität
- Ambition, unternehmerisch Verantwortung zu übernehmen

**Klingt nach Ihrem nächsten Karriereschritt?**

Dann freuen wir uns auf Ihre Bewerbung!

Ihr Ansprechpartner: **Herr Jeub**

Bitte senden Sie Ihre Unterlagen unter Angabe der **Kennziffer 50713** an: [<EMAIL>](mailto:<EMAIL>)

Für den Erstkontakt genügt ein kurzes Anschreiben und Ihr Lebenslauf, gerne mit Angaben zu Gehaltsvorstellung und Kündigungsfrist. Diskretion sichern wir selbstverständlich zu.

[Auf diese Position per E-Mail bewerben](mailto:<EMAIL>?subject=Bewerbung%20auf%20Kennziffer%2050713)
"""

def test_llm_extraction():
    """Test the LLM job extraction with the actual PSP.de content."""
    
    print("=" * 80)
    print("🧪 TESTING LLM JOB EXTRACTION ON PSP.DE CONTENT")
    print("=" * 80)
    
    # Check OpenAI API key
    openai_api_key = os.getenv('OPENAI_API_KEY')
    if not openai_api_key:
        print("❌ OPENAI_API_KEY not found in environment")
        return
    
    client = openai.OpenAI(api_key=openai_api_key)
    
    # Use the exact same prompt as the scraper
    source_url = "https://www.psp.de/stellenausschreibungen.html"
    
    prompt = f"""
    Extract ONLY actual job postings from this content. Focus on complete job descriptions with detailed information.

    IMPORTANT RULES:
    1. Only extract jobs that have DETAILED descriptions, requirements, or responsibilities
    2. Ignore navigation menus, breadcrumbs, image alt text, and lists of job titles without details
    3. Ignore content that appears to be just job title lists or category navigation
    4. Each job must have substantial content beyond just a title and location
    5. If you find multiple jobs on the same page, each must have its own unique detail URL
    6. If jobs share the same URL, only extract the MAIN job posting from that page

    Look for these indicators of a REAL job posting:
    - Detailed job description or responsibilities section
    - Requirements or qualifications section
    - Salary information or benefits
    - Application instructions or contact information
    - Company description related to the specific role

    For each ACTUAL job posting found, return:
    {{
        "title": "exact job title from the main job posting",
        "company": "company name if found, otherwise 'Not specified'",
        "location": "location if found, otherwise 'Not specified'",
        "summary": "brief summary from actual job description (max 150 chars)",
        "detail_url": "direct job URL if found, otherwise '{source_url}'"
    }}

    Return ONLY a valid JSON array. If you only find one main job posting, return an array with one job.
    Do NOT extract jobs from navigation elements, image alt text, or title lists.

    Content:
    {PSP_SAMPLE_CONTENT[:8000]}
    """
    
    print("🤖 Sending PSP.de content to LLM for job extraction...")
    print(f"📄 Content length: {len(PSP_SAMPLE_CONTENT)} characters")
    print(f"📄 Content preview:")
    print(f"   {PSP_SAMPLE_CONTENT[:200]}...")
    print()
    
    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a job listing extraction expert. You MUST return valid JSON array format. Extract ALL job postings from the content, even if information is incomplete."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=4000,
            temperature=0.0
        )
        
        response_text = response.choices[0].message.content.strip()
        print(f"✅ LLM Response received")
        print(f"📝 Response length: {len(response_text)} characters")
        print(f"📄 Full LLM Response:")
        print("-" * 40)
        print(response_text)
        print("-" * 40)
        print()
        
        # Try to parse the JSON response
        try:
            import re
            
            # First try: direct JSON parsing
            jobs_data = json.loads(response_text)
            print(f"✅ Successfully parsed JSON directly: {len(jobs_data)} jobs found")
            
        except json.JSONDecodeError:
            try:
                # Second try: extract JSON array from response
                json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
                if json_match:
                    jobs_data = json.loads(json_match.group())
                    print(f"✅ Extracted JSON from response: {len(jobs_data)} jobs found")
                else:
                    print(f"❌ No JSON array found in LLM response")
                    return
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {str(e)}")
                return
        
        # Display extracted jobs
        print(f"\n📋 EXTRACTED JOBS:")
        for i, job in enumerate(jobs_data):
            print(f"   Job {i+1}:")
            print(f"      Title: {job.get('title', 'No title')}")
            print(f"      Company: {job.get('company', 'Not specified')}")
            print(f"      Location: {job.get('location', 'Not specified')}")
            print(f"      Summary: {job.get('summary', 'No summary')}")
            print(f"      Detail URL: {job.get('detail_url', 'No URL')}")
            print()
        
        # Test the job processing pipeline
        print(f"🔧 TESTING JOB PROCESSING PIPELINE:")
        
        # Simulate the job processing from scraper.py
        source_prefix = "AAD"  # PSP.de prefix
        processed_jobs = []
        
        for i, job_data in enumerate(jobs_data[:20]):  # Limit to 20 jobs per page
            job_id = f"{source_prefix}{1:02d}{i+1:02d}"  # e.g., AAD0101, AAD0102
            
            # Get the job URL
            job_url = job_data.get("detail_url", job_data.get("link", source_url))
            
            job = {
                "id": job_id,
                "title": job_data.get("title", "Unknown Position"),
                "company": job_data.get("company", "Not specified"),
                "location": job_data.get("location", "Not specified"),
                "description": job_data.get("summary", "Not specified"),
                "source": source_prefix,
                "link": job_url,
                "isShortlisted": False,
                "scraped_at": datetime.now().isoformat()
            }
            processed_jobs.append(job)
        
        print(f"✅ Successfully processed {len(processed_jobs)} jobs")
        for job in processed_jobs:
            print(f"   {job['id']}: {job['title']}")
        
        # Save the processed jobs for inspection
        with open('debug_psp_extracted_jobs.json', 'w', encoding='utf-8') as f:
            json.dump(processed_jobs, f, indent=2, ensure_ascii=False)
        print(f"💾 Processed jobs saved to: debug_psp_extracted_jobs.json")
        
    except Exception as e:
        print(f"❌ Error during LLM extraction: {e}")
    
    print(f"\n" + "=" * 80)
    print("🏁 TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_llm_extraction()
