<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JoMaDe - Job Matching & Decision Engine</title>
    <!-- Chart.js for statistical visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .nav {
            text-align: center;
            margin-bottom: 30px;
        }

        .nav a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 10px 20px;
            border-radius: 25px;
            background: rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .nav a:hover, .nav a.active {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-dot.error {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced progress display styles */
        .progress-container {
            transition: all 0.3s ease;
        }

        .stat-card {
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .recent-activity-item {
            transition: background-color 0.2s ease;
        }

        .recent-activity-item:hover {
            background-color: #f8f9fa !important;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .input-group input, .input-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus, .input-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        /* URL display formatting */
        #job-urls {
            font-family: 'Courier New', Consolas, monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            white-space: pre;
            overflow-x: auto;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .job-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .job-item h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .job-item p {
            color: #666;
            margin-bottom: 8px;
        }

        .job-source {
            background: #e3f2fd;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            color: #1976d2 !important;
            font-weight: bold;
        }

        #source-filter {
            min-width: 150px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }

        #source-filter:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        #job-count-display {
            font-size: 14px;
            color: #666;
            font-style: italic;
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }

        .warning-message {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #ffeaa7;
        }

        .info-message {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #bee5eb;
        }

        .shortlisting-section {
            border-top: 2px solid #e0e0e0;
            margin-top: 30px;
            padding-top: 20px;
        }

        .shortlisted-job {
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .shortlisted-job:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
        }

        .comprehensive-analysis {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 6px;
            padding: 12px;
            margin-top: 12px;
            border-left: 4px solid #1976d2;
        }

        .confidence-change {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 8px;
        }

        .confidence-improved {
            background-color: #4caf50;
            color: white;
        }

        .confidence-slight-decrease {
            background-color: #ff9800;
            color: white;
        }

        .confidence-decreased {
            background-color: #f44336;
            color: white;
        }

        .pros-cons-list {
            margin: 8px 0;
            padding-left: 20px;
        }

        .pros-cons-list li {
            margin-bottom: 4px;
            font-size: 0.9em;
        }

        .pros-list li {
            color: #2e7d32;
        }

        .cons-list li {
            color: #c62828;
        }

        .match-score {
            background: #4caf50;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: bold;
            white-space: nowrap;
        }

        .shortlist-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .shortlist-controls .input-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .shortlist-controls input {
            width: 70px;
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .shortlist-controls label {
            margin: 0;
            font-size: 0.9em;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>JoMaDe</h1>
            <p>Job Matching & Decision Engine - Simplified</p>
        </div>

        <div class="nav">
            <a href="index.html" class="active">Dashboard</a>
            <a href="settings.html">Settings</a>
        </div>

        <!-- System Status -->
        <div class="card">
            <h2>System Status</h2>
            <div class="status">
                <div class="status-dot" id="backend-status"></div>
                <span id="backend-text">Checking backend connection...</span>
            </div>
            <button class="btn" onclick="checkBackendHealth()">Refresh Status</button>
        </div>

        <!-- Job URLs Management -->
        <div class="card">
            <h2>Job URLs</h2>
            <div class="input-group">
                <label for="job-urls">Job URLs (one per line):</label>
                <textarea id="job-urls" rows="6" placeholder="Enter job URLs here, one per line..."></textarea>
            </div>
            <button class="btn" onclick="loadJobUrls()">Load URLs</button>
            <button class="btn" onclick="saveJobUrls()">Save URLs</button>
            <div id="url-message"></div>
        </div>

        <!-- CV Summary -->
        <div class="card">
            <h2>CV Summary</h2>
            <div class="input-group">
                <label for="cv-summary">Your CV Summary:</label>
                <textarea id="cv-summary" rows="4" placeholder="Enter your CV summary here..."></textarea>
            </div>
            <button class="btn" onclick="saveCvSummary()">Save CV Summary</button>
            <div id="cv-message"></div>
        </div>

        <!-- Job Scraping -->
        <div class="card">
            <h2>Job Scraping</h2>
            <p>Scrape jobs from the URLs you've configured above with smart caching and duplicate detection.</p>

            <!-- Scraping Stats -->
            <div id="scraping-stats" style="display: none; margin-bottom: 15px;">
                <div style="background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; padding: 12px;">
                    <small id="stats-content" style="color: #1976d2;"></small>
                </div>
            </div>

            <!-- Age Filter Controls -->
            <div style="display: flex; gap: 15px; margin-bottom: 15px; flex-wrap: wrap; align-items: center;">
                <div class="input-group" style="display: flex; align-items: center; gap: 8px; margin-bottom: 0;">
                    <label for="age-filter" style="margin: 0; font-size: 0.9em; white-space: nowrap;"
                           title="Only scrape URLs that were last scraped X days ago or older. Set to 0 to scrape all URLs regardless of last scrape date.">
                        Include URLs older than:
                    </label>
                    <input type="number" id="age-filter" min="0" max="365" value="1"
                           style="width: 70px; padding: 6px; border: 1px solid #ddd; border-radius: 4px;"
                           onchange="updateFilterPreview()"
                           title="Days since last scrape (0 = all URLs)">
                    <span style="font-size: 0.9em; color: #666;">days</span>
                </div>
                <div id="filter-preview" style="font-size: 0.85em; color: #666; font-style: italic;">
                    Loading filter preview...
                </div>
            </div>

            <!-- Scraping Controls -->
            <div style="display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;">
                <button class="btn" onclick="scrapeJobs()" id="scrape-btn">Start Scraping</button>
                <button class="btn" onclick="scrapeJobs(true)" id="force-scrape-btn"
                        style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);"
                        title="Force scraping even if done today">
                    Force Scrape
                </button>
                <button class="btn" onclick="loadScrapingStats()" id="refresh-stats-btn"
                        style="background: linear-gradient(135deg, #607d8b 0%, #455a64 100%); font-size: 0.9em; padding: 8px 16px;"
                        title="Refresh statistics">
                    📊 Stats
                </button>
                <button class="btn" onclick="redownloadFromFirecrawl()" id="redownload-btn"
                        style="background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%); font-size: 0.9em; padding: 8px 16px;"
                        title="Redownload completed crawls from Firecrawl (no new crawls)">
                    🔁 Redownload from Firecrawl
                </button>
                <button class="btn" onclick="testProgressDisplay()" id="test-progress-btn"
                        style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); font-size: 0.8em; padding: 6px 12px;"
                        title="Test progress display">
                    🧪 Test Progress
                </button>
                <button class="btn" onclick="testSSEConnection()" id="test-sse-btn"
                        style="background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); font-size: 0.8em; padding: 6px 12px;"
                        title="Test real-time logging connection">
                    📡 Test SSE
                </button>
                <button class="btn" onclick="testUIFunctions()" id="test-ui-btn"
                        style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); font-size: 0.8em; padding: 6px 12px;"
                        title="Test UI logging functions">
                    🧪 Test UI
                </button>
            </div>

            <div id="scrape-message"></div>
            <div id="scrape-progress" style="display: none;">
                <!-- Enhanced Progress Display -->
                <div class="progress-container" style="
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 12px;
                    padding: 20px;
                    margin: 15px 0;
                ">
                    <!-- Overall Progress -->
                    <div class="overall-progress" style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <h4 style="margin: 0; color: #333;">🚀 Scraping Progress</h4>
                            <span id="progress-counter" style="font-weight: bold; color: #666;">0 / 0 URLs</span>
                        </div>
                        <div class="progress-bar-container" style="
                            background: #e9ecef;
                            border-radius: 10px;
                            height: 20px;
                            overflow: hidden;
                            position: relative;
                        ">
                            <div id="progress-bar" style="
                                background: linear-gradient(90deg, #4caf50 0%, #45a049 100%);
                                height: 100%;
                                width: 0%;
                                transition: width 0.3s ease;
                                border-radius: 10px;
                            "></div>
                            <div id="progress-text" style="
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%, -50%);
                                font-size: 0.8em;
                                font-weight: bold;
                                color: #333;
                                text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
                            ">0%</div>
                        </div>
                    </div>

                    <!-- Current Activity -->
                    <div class="current-activity" style="margin-bottom: 20px;">
                        <div id="current-status" style="
                            display: flex;
                            align-items: center;
                            gap: 10px;
                            padding: 12px;
                            background: #fff;
                            border-radius: 8px;
                            border-left: 4px solid #2196f3;
                        ">
                            <div class="activity-spinner" style="
                                width: 16px;
                                height: 16px;
                                border: 2px solid #f3f3f3;
                                border-top: 2px solid #2196f3;
                                border-radius: 50%;
                                animation: spin 1s linear infinite;
                            "></div>
                            <span id="current-activity-text">Initializing scraper...</span>
                        </div>
                    </div>

                    <!-- Statistics Summary -->
                    <div class="stats-summary" style="
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                        gap: 15px;
                        margin-bottom: 20px;
                    ">
                        <div class="stat-card" style="
                            background: #fff;
                            padding: 12px;
                            border-radius: 8px;
                            text-align: center;
                            border: 1px solid #e0e0e0;
                        ">
                            <div style="font-size: 1.5em; font-weight: bold; color: #4caf50;" id="successful-count">0</div>
                            <div style="font-size: 0.8em; color: #666;">Successful</div>
                        </div>
                        <div class="stat-card" style="
                            background: #fff;
                            padding: 12px;
                            border-radius: 8px;
                            text-align: center;
                            border: 1px solid #e0e0e0;
                        ">
                            <div style="font-size: 1.5em; font-weight: bold; color: #f44336;" id="failed-count">0</div>
                            <div style="font-size: 0.8em; color: #666;">Failed</div>
                        </div>
                        <div class="stat-card" style="
                            background: #fff;
                            padding: 12px;
                            border-radius: 8px;
                            text-align: center;
                            border: 1px solid #e0e0e0;
                        ">
                            <div style="font-size: 1.5em; font-weight: bold; color: #2196f3;" id="total-jobs-count">0</div>
                            <div style="font-size: 0.8em; color: #666;">Total Jobs</div>
                        </div>
                    </div>

                    <!-- Recent Activity Log -->
                    <div class="recent-activity" style="margin-top: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h5 style="margin: 0; color: #666;">Recent Activity</h5>
                            <button onclick="toggleDetailedLogs()" id="toggle-logs-btn" style="
                                background: none;
                                border: 1px solid #ddd;
                                border-radius: 4px;
                                padding: 4px 8px;
                                font-size: 0.8em;
                                cursor: pointer;
                                color: #666;
                            ">Show Details</button>
                        </div>
                        <div id="recent-activity-list" style="
                            max-height: 150px;
                            overflow-y: auto;
                            background: #fff;
                            border-radius: 6px;
                            border: 1px solid #e0e0e0;
                        "></div>
                    </div>

                    <!-- Detailed Logs (Hidden by default) -->
                    <div id="detailed-logs" style="display: none; margin-top: 15px;">
                        <h5 style="margin: 0 0 10px 0; color: #666;">Detailed Logs</h5>
                        <div id="log-container" style="
                            background: #fff;
                            border: 1px solid #dee2e6;
                            border-radius: 6px;
                            padding: 12px;
                            max-height: 200px;
                            overflow-y: auto;
                            font-family: 'Courier New', monospace;
                            font-size: 0.8em;
                            white-space: pre-wrap;
                            color: #333;
                        "></div>
                    </div>
                </div>
            </div>

            <!-- Job Shortlisting Section -->
            <div class="shortlisting-section" style="margin-top: 30px; border-top: 2px solid #e0e0e0; padding-top: 20px;">
                <h3>🎯 Job Shortlisting</h3>
                <p>Find the best job matches based on your CV. Make sure you have saved your CV summary above.</p>

                <div class="shortlist-controls" style="margin-bottom: 15px;">
                    <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                        <div class="input-group" style="display: flex; align-items: center; gap: 5px;">
                            <label for="min-score" style="margin: 0; font-size: 0.9em;" title="Temperature level for LLM matching">Min Score:</label>
                            <input type="number" id="min-score" min="0" max="1" step="0.1" value="0.7"
                                   style="width: 70px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div class="input-group" style="display: flex; align-items: center; gap: 5px;">
                            <label for="min-confidence" style="margin: 0; font-size: 0.9em;" title="Minimum confidence percentage to display">Min Confidence:</label>
                            <input type="number" id="min-confidence" min="0" max="100" step="5" value="75"
                                   style="width: 70px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                            <span style="font-size: 0.8em; color: #666;">%</span>
                        </div>
                        <button class="btn" onclick="shortlistJobs()" id="shortlist-btn"
                                style="background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);">
                            🎯 Find Best Matches
                        </button>
                        <button class="btn" onclick="comprehensiveMatching()" id="comprehensive-btn"
                                style="background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%); display: none;">
                            🔍 Full Matching
                        </button>
                        <button class="btn" onclick="refreshShortlist()" id="refresh-btn"
                                style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); display: none;">
                            🔄 Refresh Shortlist
                        </button>
                    </div>
                </div>

                <div id="shortlist-message"></div>

                <!-- CV Change Status -->
                <div id="cv-status" style="display: none; margin: 10px 0; padding: 10px; border-radius: 5px; font-size: 0.9em;">
                    <span id="cv-status-text"></span>
                </div>

                <!-- Shortlisted Jobs Display -->
                <div id="shortlisted-jobs" class="shortlisted-jobs-container" style="display: none; margin-top: 20px;">
                    <h4>🌟 Best Job Matches</h4>
                    <div id="shortlisted-jobs-list"></div>
                </div>
            </div>
        </div>

        <!-- Job Statistics & Categorization -->
        <div class="card">
            <h2>📊 Job Statistics & Categorization</h2>
            <p>Interactive visualization of job distribution across functional areas with filtering capabilities.</p>

            <!-- Statistics Controls -->
            <div style="display: flex; gap: 15px; align-items: center; margin-bottom: 20px; flex-wrap: wrap;">
                <button class="btn" onclick="loadJobStatistics()" id="load-stats-btn">Load Statistics</button>
                <button class="btn" onclick="refreshJobStatistics()" id="refresh-stats-btn"
                        style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);">
                    🔄 Refresh
                </button>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label for="chart-type" style="font-weight: bold;">Chart Type:</label>
                    <select id="chart-type" onchange="updateChartType()" style="padding: 8px; border-radius: 4px; border: 1px solid #ddd;">
                        <option value="doughnut">Doughnut Chart</option>
                        <option value="bar">Bar Chart</option>
                        <option value="pie">Pie Chart</option>
                    </select>
                </div>
                <div id="stats-summary" style="color: #666; font-style: italic; font-size: 0.9em;"></div>
            </div>

            <!-- Chart Container -->
            <div id="statistics-container" style="display: none;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                    <!-- Chart Section -->
                    <div style="background: #f8f9fa; border-radius: 12px; padding: 20px;">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #333;">Job Distribution by Functional Area</h3>
                        <div style="position: relative; height: 400px; display: flex; justify-content: center; align-items: center;">
                            <canvas id="categoryChart" style="max-width: 100%; max-height: 100%;"></canvas>
                        </div>
                    </div>

                    <!-- Category Legend & Filters -->
                    <div style="background: #f8f9fa; border-radius: 12px; padding: 20px;">
                        <h3 style="margin-bottom: 20px; color: #333;">Categories & Filters</h3>
                        <div id="category-legend" style="max-height: 350px; overflow-y: auto;"></div>
                    </div>
                </div>

                <!-- Detailed Statistics Table -->
                <div style="background: #fff; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden;">
                    <div style="background: #f8f9fa; padding: 15px; border-bottom: 1px solid #e0e0e0;">
                        <h4 style="margin: 0; color: #333;">📈 Detailed Category Statistics</h4>
                    </div>
                    <div style="overflow-x: auto;">
                        <table id="category-table" style="width: 100%; border-collapse: collapse;">
                            <thead style="background: #f1f3f4;">
                                <tr>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e0e0e0;">Category</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e0e0e0;">Job Count</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e0e0e0;">Percentage</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e0e0e0;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="category-table-body">
                                <!-- Table rows will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div id="stats-loading" style="display: none; text-align: center; padding: 40px;">
                <div class="spinner"></div>
                <p>Loading job statistics...</p>
            </div>

            <!-- Error/Message Display -->
            <div id="stats-message"></div>
        </div>

        <!-- 2D Job Cluster Visualization -->
        <div class="card">
            <h2>🎯 2D Job Cluster Analysis</h2>
            <p>Advanced visualization showing job distribution by category (color) and seniority level (shape) in a 2D space.</p>

            <!-- Cluster Controls -->
            <div style="display: flex; gap: 15px; align-items: center; margin-bottom: 20px; flex-wrap: wrap;">
                <button class="btn" onclick="loadClusterVisualization()" id="load-cluster-btn">Load 2D Cluster</button>
                <button class="btn" onclick="refreshClusterVisualization()" id="refresh-cluster-btn"
                        style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);">
                    🔄 Refresh
                </button>

                <!-- Seniority Filter -->
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label for="seniority-filter" style="font-weight: bold;">Filter by Seniority:</label>
                    <select id="seniority-filter" onchange="filterClusterBySeniority()" style="padding: 8px; border-radius: 4px; border: 1px solid #ddd;">
                        <option value="">All Seniority Levels</option>
                    </select>
                </div>

                <!-- Category Filter for Cluster -->
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label for="cluster-category-filter" style="font-weight: bold;">Filter by Category:</label>
                    <select id="cluster-category-filter" onchange="filterClusterByCategory()" style="padding: 8px; border-radius: 4px; border: 1px solid #ddd;">
                        <option value="">All Categories</option>
                    </select>
                </div>

                <div id="cluster-summary" style="color: #666; font-style: italic; font-size: 0.9em;"></div>
            </div>

            <!-- Cluster Visualization Container -->
            <div id="cluster-container" style="display: none;">
                <div style="background: #f8f9fa; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                    <h3 style="text-align: center; margin-bottom: 20px; color: #333;">Job Cluster: Category (Color) & Seniority (Shape)</h3>
                    <div style="position: relative; height: 500px; display: flex; justify-content: center; align-items: center;">
                        <canvas id="clusterChart" style="max-width: 100%; max-height: 100%;"></canvas>
                    </div>
                </div>

                <!-- Cluster Legends -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <!-- Category Legend -->
                    <div style="background: #fff; border: 1px solid #e0e0e0; border-radius: 8px; padding: 15px;">
                        <h4 style="margin: 0 0 15px 0; color: #333;">📊 Categories (Color)</h4>
                        <div id="cluster-category-legend-detailed" style="display: grid; grid-template-columns: 1fr; gap: 8px; max-height: 300px; overflow-y: auto;"></div>
                    </div>

                    <!-- Seniority Legend -->
                    <div style="background: #fff; border: 1px solid #e0e0e0; border-radius: 8px; padding: 15px;">
                        <h4 style="margin: 0 0 15px 0; color: #333;">🎯 Seniority Levels (Shape)</h4>
                        <div id="cluster-seniority-legend-detailed" style="display: flex; flex-direction: column; gap: 8px;"></div>
                    </div>
                </div>

                <!-- Cluster Insights -->
                <div style="background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; padding: 15px;">
                    <h4 style="margin: 0 0 10px 0; color: #1976d2;">💡 Cluster Insights</h4>
                    <div id="cluster-insights" style="color: #1976d2; font-size: 0.9em;">
                        <p>• <strong>Color</strong> indicates the job category (e.g., Finance, IT, Sales)</p>
                        <p>• <strong>Shape</strong> represents the seniority level (e.g., Junior, Lead, Executive)</p>
                        <p>• <strong>Position</strong> shows similarity - jobs closer together are more similar</p>
                        <p>• <strong>Click</strong> on any point to open the job posting</p>
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div id="cluster-loading" style="display: none; text-align: center; padding: 40px;">
                <div class="spinner"></div>
                <p>Loading 2D cluster visualization...</p>
            </div>

            <!-- Error/Message Display -->
            <div id="cluster-message"></div>
        </div>

        <!-- Job Listings -->
        <div class="card">
            <h2>Job Listings</h2>
            <div style="display: flex; gap: 15px; align-items: center; margin-bottom: 15px; flex-wrap: wrap;">
                <button class="btn" onclick="loadJobs()">Load Jobs</button>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label for="source-filter" style="font-weight: bold;">Filter by Source:</label>
                    <select id="source-filter" onchange="filterJobsBySource()" style="padding: 8px; border-radius: 4px; border: 1px solid #ddd;">
                        <option value="">All Sources</option>
                    </select>
                </div>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label for="category-filter" style="font-weight: bold;">Filter by Category:</label>
                    <select id="category-filter" onchange="filterJobsBySelectedCategory()" style="padding: 8px; border-radius: 4px; border: 1px solid #ddd;">
                        <option value="">All Categories</option>
                    </select>
                </div>
                <div id="job-count-display" style="color: #666; font-style: italic;"></div>
            </div>
            <div id="jobs-container">
                <p>Click "Load Jobs" to see available positions.</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';

        // Check backend health
        async function checkBackendHealth() {
            const statusDot = document.getElementById('backend-status');
            const statusText = document.getElementById('backend-text');


        // Redownload from Firecrawl (download-only, no new crawls)
        window.redownloadFromFirecrawl = async function redownloadFromFirecrawl() {
            const btn = document.getElementById('redownload-btn');
            try {
                btn.disabled = true;
                showMessage('scrape-message', '🔁 Starting download-only reconciliation from Firecrawl...', 'info');
                const response = await fetch(`${API_BASE}/api/redownload?skip_detail_scrape=true`, { method: 'POST' });
                if (!response.ok) {
                    const err = await response.json().catch(() => ({}));
                    throw new Error(err.message || `HTTP ${response.status}`);
                }
                const data = await response.json();
                showMessage('scrape-message', `✅ Redownload complete. Added ${data.new_jobs_added || 0} new jobs, skipped ${data.duplicates_skipped || 0} duplicates.`, 'success');
                setTimeout(() => { loadScrapingStats(); loadJobs(); }, 500);
            } catch (e) {
                showMessage('scrape-message', `❌ Redownload failed: ${e.message}`, 'error');
                console.error('Redownload error:', e);
            } finally {
                btn.disabled = false;
            }
        }

            try {
                const response = await fetch(`${API_BASE}/`);
                if (response.ok) {
                    statusDot.className = 'status-dot';
                    statusText.textContent = 'Backend is running ✓';
                } else {
                    throw new Error('Backend responded with error');
                }
            } catch (error) {
                statusDot.className = 'status-dot error';
                statusText.textContent = 'Backend is not responding ✗';
            }
        }

        // Load job URLs with statistics
        async function loadJobUrls() {
            console.log('🔄 Loading URLs from:', `${API_BASE}/job-urls`);
            try {
                const [urlsResponse, sourceStats] = await Promise.all([
                    fetch(`${API_BASE}/job-urls`),
                    loadSourceStats()
                ]);

                console.log('📡 Response status:', urlsResponse.status);

                if (urlsResponse.ok) {
                    const data = await urlsResponse.json();
                    console.log('📊 Response data:', data);
                    const urls = data.urls || [];
                    const urlData = data.url_data || [];
                    console.log('🔗 URLs found:', urls.length);

                    // Store job URLs globally for use in displayJobs
                    window.jobUrls = urlData;

                    // Create enhanced display with statistics and actual prefix
                    let urlsWithStats = urls.map((url, index) => {
                        const stats = sourceStats[url] || {
                            total_jobs: 0,
                            scraped_jobs: 0,
                            shortlisted_jobs: 0,
                            last_scraped: null
                        };

                        // Get actual prefix from URL data
                        const urlInfo = urlData.find(u => u.url === url);
                        const prefix = urlInfo ? urlInfo.prefix : `AA${String.fromCharCode(65 + index)}`;

                        // Use scraped_jobs count (since we cleaned the database, total_jobs = scraped_jobs)
                        const jobCount = stats.scraped_jobs || 0;
                        const lastScrapedText = stats.last_scraped ?
                            ` Last: ${new Date(stats.last_scraped).toLocaleDateString()}` :
                            ' Never scraped';

                        // Format with right-aligned statistics using padding
                        const baseText = `${prefix}: ${url}`;
                        const statsText = `(${jobCount} jobs)${lastScrapedText}`;

                        // Create right-aligned display with proper spacing
                        const padding = Math.max(0, 80 - baseText.length);
                        const paddedSpaces = ' '.repeat(padding);

                        return `${baseText}${paddedSpaces}${statsText}`;
                    });

                    document.getElementById('job-urls').value = urlsWithStats.join('\n');

                    // Update filter preview
                    updateFilterPreview();

                    showMessage('url-message', `Loaded ${urls.length} URLs with statistics!`, 'success');
                } else {
                    const errorText = await urlsResponse.text();
                    console.error('❌ Response error:', urlsResponse.status, errorText);
                    throw new Error(`Failed to load URLs (${urlsResponse.status})`);
                }
            } catch (error) {
                console.error('❌ Load URLs error:', error);
                showMessage('url-message', 'Error loading URLs: ' + error.message, 'error');
            }
        }



        // Update filter preview
        async function updateFilterPreview() {
            const ageFilter = document.getElementById('age-filter').value || 1;
            const previewElement = document.getElementById('filter-preview');

            try {
                const response = await fetch(`${API_BASE}/api/scrape/filter-stats?min_age_days=${ageFilter}`);
                if (response.ok) {
                    const data = await response.json();
                    const stats = data.stats;

                    previewElement.innerHTML = `
                        📊 <strong>${stats.eligible_urls}</strong> URLs will be scraped
                        (${stats.disabled_urls} disabled, ${stats.too_recent_urls} too recent)
                    `;
                    previewElement.style.color = stats.eligible_urls > 0 ? '#28a745' : '#dc3545';
                } else {
                    previewElement.innerHTML = 'Unable to load filter preview';
                    previewElement.style.color = '#666';
                }
            } catch (error) {
                console.error('Error updating filter preview:', error);
                previewElement.innerHTML = 'Filter preview unavailable';
                previewElement.style.color = '#666';
            }
        }

        // Save job URLs
        async function saveJobUrls() {
            const urls = document.getElementById('job-urls').value
                .split('\n')
                .map(url => url.trim())
                .filter(url => url.length > 0);

            try {
                const response = await fetch(`${API_BASE}/job-urls`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ urls })
                });

                if (response.ok) {
                    const data = await response.json();
                    showMessage('url-message', data.message || 'URLs saved successfully!', 'success');
                } else {
                    throw new Error('Failed to save URLs');
                }
            } catch (error) {
                showMessage('url-message', 'Error saving URLs: ' + error.message, 'error');
            }
        }

        // Save CV summary
        async function saveCvSummary() {
            const summary = document.getElementById('cv-summary').value;

            try {
                const response = await fetch(`${API_BASE}/api/cv`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ summary })
                });

                if (response.ok) {
                    showMessage('cv-message', 'CV summary saved successfully!', 'success');
                } else {
                    throw new Error('Failed to save CV summary');
                }
            } catch (error) {
                showMessage('cv-message', 'Error saving CV summary: ' + error.message, 'error');
            }
        }

        // Load scraping statistics with cache information
        async function loadScrapingStats() {
            try {
                // Load both scraping stats and cache status
                const [statsResponse, cacheResponse] = await Promise.all([
                    fetch(`${API_BASE}/api/scrape/stats`),
                    fetch(`${API_BASE}/api/scrape/cache/status`)
                ]);

                const statsResult = await statsResponse.json();
                const cacheResult = await cacheResponse.json();

                if (statsResult.success) {
                    const stats = statsResult.stats;
                    const statsContainer = document.getElementById('scraping-stats');
                    const statsContent = document.getElementById('stats-content');

                    let statsText = `📊 Total Jobs: ${stats.scraped_jobs}`;

                    if (stats.last_scrape_date) {
                        const isToday = stats.was_scraped_today;
                        const dateText = isToday ? 'Today' : stats.last_scrape_date;
                        statsText += ` | Last Scraped: ${dateText}`;

                        if (isToday) {
                            statsText += ' ✅';
                        }
                    }

                    if (stats.jobs_by_source) {
                        const sourceStats = Object.entries(stats.jobs_by_source)
                            .map(([source, count]) => `${source}: ${count}`)
                            .join(', ');
                        statsText += ` | By Source: ${sourceStats}`;
                    }

                    // Add cache information if available
                    if (cacheResult.success && cacheResult.cache_status) {
                        const cache = cacheResult.cache_status;
                        if (cache.total_cached_urls > 0) {
                            statsText += ` | 🔄 Cache: ${cache.fresh_cache_count} fresh, ${cache.expired_cache_count} expired`;
                        } else {
                            statsText += ` | 🔄 Cache: Empty`;
                        }
                    }

                    statsContent.textContent = statsText;
                    statsContainer.style.display = 'block';
                }
            } catch (error) {
                console.error('Error loading scraping stats:', error);
            }
        }

        // Enhanced scrape jobs with professional progress display
        async function scrapeJobs(forceScrap = false) {
            const btn = document.getElementById('scrape-btn');
            const forceScrapeBtn = document.getElementById('force-scrape-btn');
            const progress = document.getElementById('scrape-progress');
            const message = document.getElementById('scrape-message');

            // Prevent multiple simultaneous scraping operations
            if (btn.disabled) {
                showMessage('scrape-message', 'Scraping is already in progress. Please wait...', 'warning');
                return;
            }

            // Initialize UI
            btn.disabled = true;
            forceScrapeBtn.disabled = true;
            progress.style.display = 'block';
            message.innerHTML = '';

            // Initialize progress tracking (make it accessible to SSE handler)
            const progressState = {
                totalUrls: 0,
                completedUrls: 0,
                successfulUrls: 0,
                failedUrls: 0,
                totalJobs: 0,
                currentUrl: '',
                currentPrefix: ''
            };

            // Reset progress display
            resetProgressDisplay();
            updateProgressDisplay(progressState);

            // Show initial progress message
            const actionText = forceScrap ? 'force scraping' : 'scraping';
            showMessage('scrape-message', `Starting ${actionText} process...`, 'info');
            updateCurrentActivity('🔧 Initializing scraper...');

            let eventSource = null;
            let scrapingStarted = false;

            try {
                // Start listening to real-time logs FIRST
                console.log('🚀 STARTING EventSource connection to:', `${API_BASE}/api/scrape/logs`);

                // Test basic connectivity first
                try {
                    const testResponse = await fetch(`${API_BASE}/`);
                    console.log('🧪 Backend connectivity test:', testResponse.status, testResponse.ok);
                } catch (e) {
                    console.error('❌ Backend connectivity test failed:', e);
                    updateCurrentActivity('❌ Backend not reachable');
                    addRecentActivity('❌ Backend connectivity failed', 'error');
                    return;
                }

                eventSource = new EventSource(`${API_BASE}/api/scrape/logs`);
                console.log('🚀 EventSource created, readyState:', eventSource.readyState);
                console.log('🚀 EventSource URL:', eventSource.url);

                // Wait for EventSource to connect before starting scraping
                await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        console.log('⏰ EventSource connection timeout after 5 seconds');
                        console.log('⏰ EventSource readyState on timeout:', eventSource.readyState);
                        updateCurrentActivity('⏰ SSE connection timeout, continuing...');
                        addRecentActivity('SSE connection timeout', 'warning');
                        resolve();
                    }, 5000); // Increased timeout to 5 seconds

                    eventSource.onopen = function(event) {
                        console.log('✅ EventSource connection opened:', event);
                        console.log('✅ EventSource readyState after open:', eventSource.readyState);
                        updateCurrentActivity('🔗 Connected to real-time logging');
                        addRecentActivity('✅ SSE connection established', 'success');
                        clearTimeout(timeout);
                        // Add longer delay to ensure connection is fully stable
                        setTimeout(() => {
                            console.log('✅ SSE connection stabilized, ready for scraping');
                            updateCurrentActivity('🔗 SSE ready, starting scraping...');
                            resolve();
                        }, 500);
                    };

                    eventSource.onerror = function(event) {
                        console.error('❌ EventSource connection error:', event);
                        console.error('❌ EventSource readyState on error:', eventSource.readyState);
                        console.error('❌ EventSource URL on error:', eventSource.url);

                        // Log more details about the error
                        if (eventSource.readyState === EventSource.CLOSED) {
                            console.error('❌ EventSource is CLOSED');
                        } else if (eventSource.readyState === EventSource.CONNECTING) {
                            console.error('❌ EventSource is still CONNECTING');
                        } else if (eventSource.readyState === EventSource.OPEN) {
                            console.error('❌ EventSource is OPEN but got error');
                        }

                        clearTimeout(timeout);
                        // Don't reject here, continue with scraping even if SSE fails
                        updateCurrentActivity('⚠️ SSE connection failed, continuing without real-time logs');
                        addRecentActivity('❌ SSE connection failed', 'error');
                        resolve();
                    };
                });

                eventSource.onmessage = function(event) {
                    try {
                        console.log('🔥 RECEIVED SSE MESSAGE:', event.data);
                        const logData = JSON.parse(event.data);
                        console.log('🔥 PARSED SSE DATA:', logData);

                        // Force update UI to show we received a message
                        addRecentActivity(`📡 SSE: ${logData.message || 'Message received'}`, logData.type || 'info');
                        addDetailedLog(`[SSE-${logData.type || 'info'}] ${logData.message || 'Unknown message'}`);

                        // Handle different message types
                        switch (logData.type) {
                            case 'connected':
                                console.log('SSE: Connected to backend logging');
                                updateCurrentActivity('🔗 Connected to real-time logging');
                                break;

                            case 'heartbeat':
                                console.log('SSE: Heartbeat received - scraping_active:', logData.scraping_active, 'queue_size:', logData.queue_size);
                                // Don't process heartbeat messages further
                                return;

                            case 'complete':
                                console.log('SSE: Scraping completed');
                                updateCurrentActivity('✅ Scraping completed!');
                                eventSource.close();
                                // Auto-refresh jobs after successful scraping
                                setTimeout(() => {
                                    loadJobs();
                                }, 1000);
                                break;

                            case 'error':
                                console.error('SSE: Backend error:', logData.message);
                                updateCurrentActivity('❌ Backend error: ' + logData.message);
                                break;

                            default:
                                console.log('SSE: Processing log message:', logData);
                                // Process regular log messages
                                processLogMessage(logData, progressState);
                                break;
                        }

                    } catch (e) {
                        console.error('🚨 ERROR PARSING SSE MESSAGE:', e, 'Raw data:', event.data);
                        updateCurrentActivity('⚠️ Error processing log message');
                        addRecentActivity('❌ Error parsing SSE message', 'error');
                    }
                };

                eventSource.onerror = function(event) {
                    console.error('EventSource error during scraping:', event);
                    console.error('EventSource readyState:', eventSource.readyState);
                    updateCurrentActivity('⚠️ Real-time logging connection lost');
                };

                // Get age filter value
                const ageFilter = document.getElementById('age-filter').value || 1;

                // Build URL with force_scrape and min_age_days parameters
                const url = forceScrap ?
                    `${API_BASE}/api/scrape?force_scrape=true&min_age_days=${ageFilter}` :
                    `${API_BASE}/api/scrape?min_age_days=${ageFilter}`;

                // Test SSE connection before starting scraping
                console.log('🧪 Testing SSE connection before scraping...');
                updateCurrentActivity('🧪 Testing SSE connection...');

                // Send a test message to verify SSE is working
                try {
                    const testResponse = await fetch(`${API_BASE}/test/sse-logs`, { method: 'POST' });
                    if (testResponse.ok) {
                        console.log('✅ SSE test successful before scraping');
                        addRecentActivity('✅ SSE test successful', 'success');
                    }
                } catch (e) {
                    console.error('❌ SSE test failed before scraping:', e);
                    addRecentActivity('❌ SSE test failed', 'error');
                }

                // Wait a moment for test messages to be processed
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Start the scraping process AFTER EventSource is connected and tested
                console.log('🚀 Starting scraping request to:', url);
                updateCurrentActivity('🚀 Starting scraping process...');

                // Start a fallback progress updater in case SSE fails
                const progressUpdater = setInterval(async () => {
                    try {
                        const statsResponse = await fetch(`${API_BASE}/api/sources/stats`);
                        if (statsResponse.ok) {
                            const statsData = await statsResponse.json();
                            // Update total jobs count from stats
                            const totalJobs = Object.values(statsData.source_stats || {}).reduce((sum, stat) => sum + (stat.scraped_jobs || 0), 0);
                            if (totalJobs > progressState.totalJobs) {
                                progressState.totalJobs = totalJobs;
                                updateProgressDisplay(progressState);
                            }
                        }
                    } catch (e) {
                        console.log('Progress update failed:', e);
                    }
                }, 2000);

                const response = await fetch(url, {
                    method: 'POST'
                });

                scrapingStarted = true;

                // Clear the progress updater when scraping request completes
                clearInterval(progressUpdater);

                if (response.ok) {
                    const data = await response.json();

                    let successMessage;
                    if (data.was_cached) {
                        successMessage = `📅 Used cached data from ${data.last_scrape_date}! Total jobs: ${data.total_jobs}`;
                    } else {
                        successMessage = `✅ Fresh scraping completed! Added ${data.new_jobs_added || 0} new jobs, skipped ${data.duplicates_skipped || 0} duplicates. Total: ${data.total_jobs || data.job_count}`;
                        if (data.failed_urls && data.failed_urls.length > 0) {
                            successMessage += ` (${data.failed_urls.length} URLs failed)`;
                        }
                    }

                    showMessage('scrape-message', successMessage, 'success');

                    // Refresh stats after scraping
                    setTimeout(() => {
                        loadScrapingStats();
                    }, 500);
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.message || `HTTP ${response.status}: Failed to scrape jobs`);
                }
            } catch (error) {
                if (error.message.includes('fetch')) {
                    showMessage('scrape-message', 'Network error: Unable to connect to the backend server. Please check if the server is running.', 'error');
                } else {
                    showMessage('scrape-message', 'Error scraping jobs: ' + error.message, 'error');
                }
                console.error('Scraping error:', error);
            } finally {
                btn.disabled = false;
                forceScrapeBtn.disabled = false;


                progress.style.display = 'none';
                if (eventSource) {
                    eventSource.close();
                }
            }
        }

        // Helper functions for enhanced progress display
        function resetProgressDisplay() {
            // Reset progress bar
            document.getElementById('progress-bar').style.width = '0%';
            document.getElementById('progress-text').textContent = '0%';
            document.getElementById('progress-counter').textContent = '0 / 0 URLs';

            // Reset statistics
            document.getElementById('successful-count').textContent = '0';
            document.getElementById('failed-count').textContent = '0';
            document.getElementById('total-jobs-count').textContent = '0';

            // Reset activity
            document.getElementById('current-activity-text').textContent = 'Initializing...';
            document.getElementById('recent-activity-list').innerHTML = '';
            document.getElementById('log-container').innerHTML = '';

            // Hide detailed logs by default
            document.getElementById('detailed-logs').style.display = 'none';
            document.getElementById('toggle-logs-btn').textContent = 'Show Details';
        }

        function updateProgressDisplay(state) {
            console.log('Updating progress display with state:', state);
            // Update progress bar
            const percentage = state.totalUrls > 0 ? Math.round((state.completedUrls / state.totalUrls) * 100) : 0;
            document.getElementById('progress-bar').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = percentage + '%';
            document.getElementById('progress-counter').textContent = `${state.completedUrls} / ${state.totalUrls} URLs`;

            // Update statistics
            document.getElementById('successful-count').textContent = state.successfulUrls;
            document.getElementById('failed-count').textContent = state.failedUrls;
            document.getElementById('total-jobs-count').textContent = state.totalJobs;
        }

        function updateCurrentActivity(message) {
            console.log('Updating current activity:', message);
            document.getElementById('current-activity-text').textContent = message;
        }

        function addRecentActivity(message, type = 'info') {
            console.log('addRecentActivity called with:', message, 'type:', type);
            const activityList = document.getElementById('recent-activity-list');
            if (!activityList) {
                console.error('recent-activity-list element not found!');
                return;
            }
            const timestamp = new Date().toLocaleTimeString();

            // Create activity item
            const activityItem = document.createElement('div');
            activityItem.style.cssText = `
                padding: 8px 12px;
                border-bottom: 1px solid #f0f0f0;
                font-size: 0.85em;
                display: flex;
                align-items: center;
                gap: 8px;
            `;

            // Add icon based on type
            let icon = '📋';
            let color = '#666';
            if (type === 'success') {
                icon = '✅';
                color = '#4caf50';
            } else if (type === 'error') {
                icon = '❌';
                color = '#f44336';
            } else if (type === 'warning') {
                icon = '⚠️';
                color = '#ff9800';
            }

            activityItem.innerHTML = `
                <span style="color: ${color};">${icon}</span>
                <span style="color: #999; font-size: 0.8em;">[${timestamp}]</span>
                <span style="flex: 1;">${message}</span>
            `;

            // Add to top of list
            activityList.insertBefore(activityItem, activityList.firstChild);

            // Keep only last 10 items
            while (activityList.children.length > 10) {
                activityList.removeChild(activityList.lastChild);
            }
        }

        function addDetailedLog(message) {
            console.log('addDetailedLog called with:', message);
            const logContainer = document.getElementById('log-container');
            if (!logContainer) {
                console.error('log-container element not found!');
                return;
            }
            const timestamp = new Date().toLocaleTimeString();
            const logLine = `[${timestamp}] ${message}\n`;

            logContainer.textContent += logLine;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log('Log added to container, current content length:', logContainer.textContent.length);
        }

        function processLogMessage(logData, progressState) {
            console.log('Processing log message:', logData);
            const message = logData.message;

            // Add to detailed logs
            addDetailedLog(message);

            // Parse different types of messages and update progress
            if (message.includes('Found') && message.includes('URLs to scrape')) {
                const match = message.match(/Found (\d+) URLs/);
                if (match) {
                    progressState.totalUrls = parseInt(match[1]);
                    updateProgressDisplay(progressState);
                    updateCurrentActivity(`📋 Found ${progressState.totalUrls} URLs to scrape`);
                    addRecentActivity(`Found ${progressState.totalUrls} URLs to scrape`, 'info');

                    // Handle case where no URLs are found
                    if (progressState.totalUrls === 0) {
                        updateCurrentActivity('❌ No URLs configured for scraping');
                        addRecentActivity('No URLs configured for scraping', 'error');


                    }
                }
            }
            else if (message.includes('Processing') && message.includes('/')) {
                const match = message.match(/Processing (\d+)\/(\d+): (\w+)/);
                if (match) {
                    progressState.currentPrefix = match[3];
                    updateCurrentActivity(`🔄 Processing ${match[3]} (${match[1]}/${match[2]})`);
                }
            }
            else if (message.includes('CRAWLING:')) {
                const match = message.match(/CRAWLING: (.+?) \(prefix: (\w+)\)/);
                if (match) {
                    progressState.currentUrl = match[1];
                    progressState.currentPrefix = match[2];
                    updateCurrentActivity(`🕷️ Crawling ${match[2]}: ${match[1].substring(0, 50)}...`);
                    addRecentActivity(`Started crawling ${match[2]}`, 'info');
                }
            }
            else if (message.includes('Success:') && message.includes('jobs added')) {
                const match = message.match(/Success: (\d+) jobs added \(Total: (\d+)\)/);
                if (match) {
                    const jobsAdded = parseInt(match[1]);
                    progressState.totalJobs = parseInt(match[2]);
                    progressState.successfulUrls++;
                    progressState.completedUrls++;
                    updateProgressDisplay(progressState);
                    addRecentActivity(`✅ ${progressState.currentPrefix}: Found ${jobsAdded} jobs`, 'success');
                }
            }
            else if (message.includes('Failed: No jobs found')) {
                progressState.failedUrls++;
                progressState.completedUrls++;
                updateProgressDisplay(progressState);
                addRecentActivity(`❌ ${progressState.currentPrefix}: No jobs found`, 'error');
            }
            else if (message.includes('ERROR') || message.includes('Failed')) {
                addRecentActivity(message.substring(0, 80) + '...', 'error');
            }
            else if (message.includes('⚠️') || message.includes('WARNING')) {
                addRecentActivity(message.substring(0, 80) + '...', 'warning');
            }
        }

        function toggleDetailedLogs() {
            const detailedLogs = document.getElementById('detailed-logs');
            const toggleBtn = document.getElementById('toggle-logs-btn');

            if (detailedLogs.style.display === 'none') {
                detailedLogs.style.display = 'block';
                toggleBtn.textContent = 'Hide Details';
            } else {
                detailedLogs.style.display = 'none';
                toggleBtn.textContent = 'Show Details';
            }
        }

        // Test function to verify progress display is working
        function testProgressDisplay() {
            console.log('Testing progress display...');

            // Show progress container
            const progress = document.getElementById('scrape-progress');
            progress.style.display = 'block';

            // Reset and test progress display
            resetProgressDisplay();

            // Test progress state
            const testState = {
                totalUrls: 10,
                completedUrls: 3,
                successfulUrls: 2,
                failedUrls: 1,
                totalJobs: 25,
                currentUrl: 'https://example.com',
                currentPrefix: 'TEST'
            };

            updateProgressDisplay(testState);
            updateCurrentActivity('🧪 Testing progress display functionality');
            addRecentActivity('Test activity 1', 'info');
            addRecentActivity('Test success', 'success');
            addRecentActivity('Test warning', 'warning');
            addRecentActivity('Test error', 'error');
            addDetailedLog('Test detailed log message');

            console.log('Progress display test completed');

            // Hide after 10 seconds
            setTimeout(() => {
                progress.style.display = 'none';
                console.log('Test progress display hidden');
            }, 10000);
        }

        // Test SSE connection
        async function testSSEConnection() {
            console.log('Testing SSE connection...');

            const progress = document.getElementById('scrape-progress');
            progress.style.display = 'block';

            resetProgressDisplay();
            updateCurrentActivity('📡 Testing SSE connection...');

            // Test basic connectivity first
            try {
                const testResponse = await fetch(`${API_BASE}/`);
                console.log('🧪 SSE Test: Backend connectivity:', testResponse.status, testResponse.ok);
                addRecentActivity(`Backend connectivity: ${testResponse.status}`, testResponse.ok ? 'success' : 'error');
            } catch (e) {
                console.error('❌ SSE Test: Backend connectivity failed:', e);
                addRecentActivity('Backend connectivity failed', 'error');
                updateCurrentActivity('❌ Backend not reachable');
                return;
            }

            // Create SSE connection
            console.log('🔌 SSE Test: Creating EventSource to:', `${API_BASE}/api/scrape/logs`);
            const eventSource = new EventSource(`${API_BASE}/api/scrape/logs`);
            console.log('🔌 SSE Test: EventSource created, readyState:', eventSource.readyState);

            eventSource.onopen = function(event) {
                console.log('✅ SSE Test: Connection opened', event);
                console.log('✅ SSE Test: EventSource readyState:', eventSource.readyState);
                updateCurrentActivity('✅ SSE connection established');
                addRecentActivity('✅ SSE connection opened', 'success');
            };

            eventSource.onmessage = function(event) {
                console.log('📨 SSE Test: Message received:', event.data);
                try {
                    const logData = JSON.parse(event.data);
                    addRecentActivity(logData.message || 'SSE message received', logData.type || 'info');
                    addDetailedLog(`[${logData.type}] ${logData.message}`);

                    if (logData.type === 'complete') {
                        updateCurrentActivity('✅ SSE test completed');
                        eventSource.close();
                    }
                } catch (e) {
                    console.error('❌ SSE Test: Error parsing message:', e);
                    addRecentActivity('Error parsing SSE message', 'error');
                }
            };

            eventSource.onerror = function(event) {
                console.error('❌ SSE Test: Connection error', event);
                console.error('❌ SSE Test: EventSource readyState:', eventSource.readyState);
                console.error('❌ SSE Test: EventSource URL:', eventSource.url);
                updateCurrentActivity('❌ SSE connection error');
                addRecentActivity('SSE connection failed', 'error');
            };

            // Trigger test messages from backend
            try {
                updateCurrentActivity('📤 Sending test messages...');
                const response = await fetch(`${API_BASE}/test/sse-logs`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('SSE Test: Backend response:', data);
                    addRecentActivity('Test messages sent to backend', 'success');
                } else {
                    throw new Error('Failed to trigger test messages');
                }
            } catch (error) {
                console.error('SSE Test: Error triggering test messages:', error);
                updateCurrentActivity('❌ Failed to trigger test messages');
                addRecentActivity('Failed to send test messages', 'error');
            }

            // Close connection after 15 seconds
            setTimeout(() => {
                if (eventSource.readyState !== EventSource.CLOSED) {
                    eventSource.close();
                    updateCurrentActivity('🔌 SSE test connection closed');
                }
                progress.style.display = 'none';
            }, 15000);
        }

        // Test UI functions
        function testUIFunctions() {
            console.log('Testing UI functions...');

            // Show progress display
            const progress = document.getElementById('scrape-progress');
            progress.style.display = 'block';

            // Test updateCurrentActivity
            updateCurrentActivity('🧪 Testing UI functions...');

            // Test addRecentActivity
            addRecentActivity('Test message 1: Info', 'info');
            addRecentActivity('Test message 2: Success', 'success');
            addRecentActivity('Test message 3: Warning', 'warning');
            addRecentActivity('Test message 4: Error', 'error');

            // Test addDetailedLog
            addDetailedLog('Test detailed log message 1');
            addDetailedLog('Test detailed log message 2');
            addDetailedLog('Test detailed log message 3');

            // Show detailed logs
            const detailedLogs = document.getElementById('detailed-logs');
            detailedLogs.style.display = 'block';
            document.getElementById('toggle-logs-btn').textContent = 'Hide Details';

            console.log('UI test completed');
        }

        // Load source statistics for URL management
        async function loadSourceStats() {
            try {
                const response = await fetch(`${API_BASE}/api/sources/stats`);
                if (response.ok) {
                    const data = await response.json();
                    return data.source_stats;
                } else {
                    console.error('Failed to load source statistics');
                    return {};
                }
            } catch (error) {
                console.error('Error loading source stats:', error);
                return {};
            }
        }

        // Check CV change status and update UI
        async function checkCVChangeStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/shortlist/cv-status`);
                if (response.ok) {
                    const data = await response.json();
                    const status = data.status;
                    const cvStatusDiv = document.getElementById('cv-status');
                    const cvStatusText = document.getElementById('cv-status-text');
                    const refreshBtn = document.getElementById('refresh-btn');

                    if (status.needs_refresh) {
                        cvStatusDiv.style.display = 'block';
                        cvStatusDiv.style.background = '#fff3cd';
                        cvStatusDiv.style.border = '1px solid #ffeaa7';
                        cvStatusDiv.style.color = '#856404';
                        cvStatusText.textContent = `⚠️ Your CV has changed since the last shortlisting. Current shortlist has ${status.active_entries} active entries. Click "Refresh Shortlist" to update matches.`;
                        refreshBtn.style.display = 'inline-block';
                        return false; // CV changed, don't load shortlist
                    } else if (status.has_shortlist && !status.cv_changed) {
                        cvStatusDiv.style.display = 'block';
                        cvStatusDiv.style.background = '#d4edda';
                        cvStatusDiv.style.border = '1px solid #c3e6cb';
                        cvStatusDiv.style.color = '#155724';
                        cvStatusText.textContent = `✅ Shortlist is up-to-date with your current CV (${status.active_entries} active entries).`;
                        refreshBtn.style.display = 'none';

                        // Load and display the existing shortlist
                        const shortlistLoaded = await loadExistingShortlist();
                        return shortlistLoaded;
                    } else {
                        cvStatusDiv.style.display = 'none';
                        refreshBtn.style.display = 'none';
                        return false; // No shortlist available
                    }
                }
                return false;
            } catch (error) {
                console.error('Error checking CV status:', error);
                return false;
            }
        }

        // Load and display existing shortlist
        async function loadExistingShortlist() {
            try {
                const minConfidence = parseFloat(document.getElementById('min-confidence').value) || 75.0;
                const response = await fetch(`${API_BASE}/api/shortlist?min_confidence=${minConfidence}`);

                if (response.ok) {
                    const data = await response.json();
                    const shortlistedJobs = data.shortlist;

                    if (shortlistedJobs && shortlistedJobs.length > 0) {
                        displayShortlistedJobs(shortlistedJobs);
                        showMessage('shortlist-message',
                            `✅ Loaded ${shortlistedJobs.length} cached job matches (confidence ≥${minConfidence}%)`,
                            'success');

                        // Show comprehensive matching button when loading cached shortlist
                        const comprehensiveBtn = document.getElementById('comprehensive-btn');
                        if (comprehensiveBtn) {
                            comprehensiveBtn.style.display = 'inline-block';
                            console.log('Comprehensive button made visible from cached shortlist');
                        } else {
                            console.error('Comprehensive button element not found in cached shortlist!');
                        }

                        return true; // Indicate shortlist was loaded
                    }
                }
                return false; // Indicate no shortlist was loaded
            } catch (error) {
                console.error('Error loading existing shortlist:', error);
                return false;
            }
        }

        // Display shortlisted jobs (extracted from shortlistJobs function)
        function displayShortlistedJobs(shortlistedJobs) {
            const shortlistedContainer = document.getElementById('shortlisted-jobs');
            const shortlistedList = document.getElementById('shortlisted-jobs-list');

            // Convert shortlist format to job format for display
            const jobsForDisplay = shortlistedJobs.map(entry => {
                const job = entry.job_data || entry;
                // Add timestamp information
                job.shortlisted_at = entry.shortlisted_at;
                job.confidence_percentage = entry.confidence_percentage;
                job.match_score = entry.match_score;
                return job;
            });

            const jobsHtml = jobsForDisplay.map(job => {
                // Create clickable title if job has a link
                const titleHtml = job.link && job.link !== 'Not specified' ?
                    `<a href="${job.link}" target="_blank" style="color: #2e7d32; text-decoration: none; hover: text-decoration: underline;">${job.title}</a>` :
                    job.title;

                // Format shortlisted timestamp
                let shortlistedDate = '';
                if (job.shortlisted_at) {
                    try {
                        const d = new Date(job.shortlisted_at);
                        shortlistedDate = `<p style="margin: 5px 0; color: #666; font-size: 0.85em;">
                            <strong>Shortlisted:</strong> ${d.toLocaleDateString()} at ${d.toLocaleTimeString()}
                        </p>`;
                    } catch (e) {
                        shortlistedDate = `<p style="margin: 5px 0; color: #666; font-size: 0.85em;">
                            <strong>Shortlisted:</strong> ${job.shortlisted_at}
                        </p>`;
                    }
                }

                return `
                <div class="shortlisted-job" style="
                    border: 1px solid #4caf50;
                    border-radius: 8px;
                    padding: 15px;
                    margin-bottom: 15px;
                    background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
                ">
                    <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 10px;">
                        <h4 style="margin: 0; color: #2e7d32; flex-grow: 1;">${titleHtml}</h4>
                        <div class="match-score" style="
                            background: #4caf50;
                            color: white;
                            padding: 4px 12px;
                            border-radius: 20px;
                            font-size: 0.85em;
                            font-weight: bold;
                            margin-left: 10px;
                        ">
                            ${job.confidence_percentage || Math.round(job.match_score * 100)}% Match
                        </div>
                    </div>
                    <p style="margin: 5px 0; color: #666; font-size: 0.9em;">
                        <strong>Source:</strong> ${job.source_display || job.source || 'Unknown'}
                    </p>
                    <p style="margin: 5px 0; color: #666; font-size: 0.9em;">
                        <strong>Location:</strong> ${job.location || 'Not specified'}
                    </p>
                    ${shortlistedDate}
                    ${job.description ? `
                        <p style="margin: 10px 0 0 0; font-size: 0.9em; line-height: 1.4;">
                            ${job.description.substring(0, 200)}${job.description.length > 200 ? '...' : ''}
                        </p>
                    ` : ''}
                </div>
                `;
            }).join('');

            shortlistedList.innerHTML = jobsHtml;
            shortlistedContainer.style.display = 'block';
        }

        // Display comprehensive analysis results
        function displayComprehensiveAnalysis(analyzedJobs) {
            const shortlistedContainer = document.getElementById('shortlisted-jobs');
            const shortlistedList = document.getElementById('shortlisted-jobs-list');

            // Convert analysis format to job format for display
            const jobsForDisplay = analyzedJobs.map(entry => {
                const job = entry.job_data || entry;
                // Add timestamp information
                job.analyzed_at = entry.analyzed_at;
                job.stage1_confidence = entry.stage1_confidence;
                job.stage2_confidence = entry.stage2_confidence;
                job.confidence_change = entry.confidence_change;
                job.pros = entry.pros || [];
                job.cons = entry.cons || [];
                return job;
            });

            const jobsHtml = jobsForDisplay.map(job => {
                // Create clickable title if job has a link
                const titleHtml = job.link && job.link !== 'Not specified' ?
                    `<a href="${job.link}" target="_blank" style="color: #2e7d32; text-decoration: none; hover: text-decoration: underline;">${job.title}</a>` :
                    job.title;

                // Format analysis timestamp
                let analyzedDate = '';
                if (job.analyzed_at) {
                    try {
                        const d = new Date(job.analyzed_at);
                        analyzedDate = `<p style="margin: 5px 0; color: #666; font-size: 0.85em;">
                            <strong>Analyzed:</strong> ${d.toLocaleDateString()} at ${d.toLocaleTimeString()}
                        </p>`;
                    } catch (e) {
                        analyzedDate = `<p style="margin: 5px 0; color: #666; font-size: 0.85em;">
                            <strong>Analyzed:</strong> ${job.analyzed_at}
                        </p>`;
                    }
                }

                // Determine confidence change indicator
                let confidenceChangeClass = '';
                let confidenceChangeText = '';
                const confidenceChange = job.confidence_change || 0;

                if (confidenceChange >= 0) {
                    confidenceChangeClass = 'confidence-improved';
                    confidenceChangeText = `+${confidenceChange.toFixed(1)}%`;
                } else if (confidenceChange > -10) {
                    confidenceChangeClass = 'confidence-slight-decrease';
                    confidenceChangeText = `${confidenceChange.toFixed(1)}%`;
                } else {
                    confidenceChangeClass = 'confidence-decreased';
                    confidenceChangeText = `${confidenceChange.toFixed(1)}%`;
                }

                // Format pros and cons lists
                const prosList = job.pros && job.pros.length > 0 ?
                    `<h5 style="margin: 10px 0 5px 0; color: #2e7d32;">Pros:</h5>
                    <ul class="pros-cons-list pros-list">
                        ${job.pros.map(pro => `<li>${pro}</li>`).join('')}
                    </ul>` : '';

                const consList = job.cons && job.cons.length > 0 ?
                    `<h5 style="margin: 10px 0 5px 0; color: #c62828;">Cons:</h5>
                    <ul class="pros-cons-list cons-list">
                        ${job.cons.map(con => `<li>${con}</li>`).join('')}
                    </ul>` : '';

                return `
                <div class="shortlisted-job" style="
                    border: 1px solid #4caf50;
                    border-radius: 8px;
                    padding: 15px;
                    margin-bottom: 15px;
                    background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
                ">
                    <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 10px;">
                        <h4 style="margin: 0; color: #2e7d32; flex-grow: 1;">${titleHtml}</h4>
                        <div class="match-score" style="
                            background: #4caf50;
                            color: white;
                            padding: 4px 12px;
                            border-radius: 20px;
                            font-size: 0.85em;
                            font-weight: bold;
                            margin-left: 10px;
                        ">
                            ${job.stage1_confidence || Math.round(job.match_score * 100)}% Initial Match
                        </div>
                    </div>
                    <p style="margin: 5px 0; color: #666; font-size: 0.9em;">
                        <strong>Source:</strong> ${job.source_display || job.source || 'Unknown'}
                    </p>
                    <p style="margin: 5px 0; color: #666; font-size: 0.9em;">
                        <strong>Location:</strong> ${job.location || 'Not specified'}
                    </p>
                    ${analyzedDate}
                    ${job.description ? `
                        <p style="margin: 10px 0 0 0; font-size: 0.9em; line-height: 1.4;">
                            ${job.description.substring(0, 200)}${job.description.length > 200 ? '...' : ''}
                        </p>
                    ` : ''}

                    <!-- Comprehensive Analysis Section -->
                    <div class="comprehensive-analysis">
                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 8px;">
                            <h5 style="margin: 0; color: #1976d2;">Job Description Match</h5>
                            <div style="display: flex; align-items: center;">
                                <div class="match-score" style="
                                    background: #1976d2;
                                    color: white;
                                    padding: 4px 12px;
                                    border-radius: 20px;
                                    font-size: 0.85em;
                                    font-weight: bold;
                                ">
                                    ${job.stage2_confidence}% Full Match
                                </div>
                                <div class="confidence-change ${confidenceChangeClass}">
                                    ${confidenceChangeText}
                                </div>
                            </div>
                        </div>
                        ${prosList}
                        ${consList}
                    </div>
                </div>
                `;
            }).join('');

            shortlistedList.innerHTML = jobsHtml;
            shortlistedContainer.style.display = 'block';
        }

        // Refresh shortlist when CV has changed
        async function refreshShortlist() {
            const refreshBtn = document.getElementById('refresh-btn');
            const originalText = refreshBtn.textContent;

            try {
                refreshBtn.disabled = true;
                refreshBtn.textContent = '🔄 Refreshing...';

                const response = await fetch(`${API_BASE}/api/shortlist/refresh`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.refreshed) {
                        showMessage('shortlist-message',
                            '✅ Shortlist refreshed! Previous entries marked as inactive. Run "Find Best Matches" to get updated results.',
                            'success');
                        // Hide the refresh button and status
                        document.getElementById('cv-status').style.display = 'none';
                        refreshBtn.style.display = 'none';
                        // Clear current shortlist display
                        document.getElementById('shortlisted-jobs').style.display = 'none';
                    } else {
                        showMessage('shortlist-message', data.message, 'info');
                    }
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}: Failed to refresh shortlist`);
                }
            } catch (error) {
                console.error('Error refreshing shortlist:', error);
                showMessage('shortlist-message', 'Error refreshing shortlist: ' + error.message, 'error');
            } finally {
                refreshBtn.disabled = false;
                refreshBtn.textContent = originalText;
            }
        }

        // Shortlist jobs based on CV match
        async function shortlistJobs() {
            const btn = document.getElementById('shortlist-btn');
            const message = document.getElementById('shortlist-message');
            const shortlistedContainer = document.getElementById('shortlisted-jobs');
            const shortlistedList = document.getElementById('shortlisted-jobs-list');

            const temp = parseFloat(document.getElementById('min-score').value);
            const minConfidence = parseFloat(document.getElementById('min-confidence').value);

            // Validate inputs
            if (isNaN(temp) || temp < 0 || temp > 1) {
                showMessage('shortlist-message', 'Please enter a valid temperature between 0 and 1', 'error');
                return;
            }

            if (isNaN(minConfidence) || minConfidence < 0 || minConfidence > 100) {
                showMessage('shortlist-message', 'Please enter a valid minimum confidence between 0 and 100', 'error');
                return;
            }

            btn.disabled = true;
            btn.textContent = '🔄 Finding Matches...';
            shortlistedContainer.style.display = 'none';
            message.innerHTML = '';

            try {
                const response = await fetch(`${API_BASE}/api/jobs/shortlist?temp=${temp}&min_confidence=${minConfidence}`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('Shortlisting API response:', data);
                    const shortlistedJobs = data.shortlisted_jobs;
                    console.log('Shortlisted jobs count:', shortlistedJobs ? shortlistedJobs.length : 'undefined');

                    if (shortlistedJobs.length === 0) {
                        showMessage('shortlist-message',
                            `No jobs found with temperature ${temp} and confidence of ${minConfidence}%. Try lowering the thresholds.`,
                            'warning');
                        // Hide comprehensive button if no shortlisted jobs
                        document.getElementById('comprehensive-btn').style.display = 'none';
                        return;
                    }

                    // Display shortlisted jobs using the shared function
                    displayShortlistedJobs(shortlistedJobs);

                    showMessage('shortlist-message',
                        `✅ Found ${shortlistedJobs.length} matching jobs out of ${data.total_jobs} total jobs!`,
                        'success');

                    // Show comprehensive matching button when shortlisting is successful
                    const comprehensiveBtn = document.getElementById('comprehensive-btn');
                    if (comprehensiveBtn) {
                        comprehensiveBtn.style.display = 'inline-block';
                        console.log('Comprehensive button should be visible now');
                    } else {
                        console.error('Comprehensive button element not found!');
                    }

                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}: Failed to shortlist jobs`);
                }

            } catch (error) {
                console.error('Error shortlisting jobs:', error);
                showMessage('shortlist-message', 'Error finding job matches: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🎯 Find Best Matches';
            }
        }

        // Comprehensive job matching (Stage 2)
        async function comprehensiveMatching() {
            const btn = document.getElementById('comprehensive-btn');
            const message = document.getElementById('shortlist-message');
            const shortlistedContainer = document.getElementById('shortlisted-jobs');
            const shortlistedList = document.getElementById('shortlisted-jobs-list');

            const temp = parseFloat(document.getElementById('min-score').value);

            // Validate temperature input
            if (isNaN(temp) || temp < 0 || temp > 1) {
                showMessage('shortlist-message', 'Please enter a valid temperature between 0 and 1', 'error');
                return;
            }

            btn.disabled = true;
            btn.textContent = '🔄 Analyzing...';

            // Show loading message
            showMessage('shortlist-message', '🔍 Performing comprehensive analysis of shortlisted jobs...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/jobs/comprehensive-match?temp=${temp}`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const data = await response.json();
                    const analyzedJobs = data.analyzed_jobs;

                    if (analyzedJobs.length === 0) {
                        showMessage('shortlist-message',
                            'No jobs available for comprehensive analysis. Please run shortlisting first.',
                            'warning');
                        return;
                    }

                    // Display comprehensive analysis results
                    displayComprehensiveAnalysis(analyzedJobs);

                    showMessage('shortlist-message',
                        `✅ Comprehensive analysis completed for ${analyzedJobs.length} jobs! ` +
                        `(${data.fresh_analyses} fresh analyses, ${data.cached_results} from cache)`,
                        'success');

                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}: Failed to perform comprehensive analysis`);
                }

            } catch (error) {
                console.error('Error in comprehensive matching:', error);
                showMessage('shortlist-message', 'Error performing comprehensive analysis: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🔍 Full Matching';
            }
        }

        // Load jobs
        async function loadJobs() {
            console.log('loadJobs() called');
            const container = document.getElementById('jobs-container');
            container.innerHTML = '<div class="loading"><div class="spinner"></div><p>Loading jobs...</p></div>';

            try {
                console.log('Fetching jobs from:', `${API_BASE}/api/jobs`);
                const response = await fetch(`${API_BASE}/api/jobs`);
                console.log('Response status:', response.status);

                if (response.ok) {
                    const jobs = await response.json();
                    console.log('Jobs received:', jobs.length, 'jobs');
                    displayJobs(jobs || []);
                } else {
                    throw new Error(`Failed to load jobs (status: ${response.status})`);
                }
            } catch (error) {
                console.error('Error loading jobs:', error);
                container.innerHTML = `<div class="error-message">Error loading jobs: ${error.message}</div>`;
            }
        }

        // Store all jobs globally for filtering
        let allJobs = [];

        // Extract domain name from URL
        function extractDomainName(url) {
            try {
                const domain = new URL(url).hostname;
                // Extract the main domain name (e.g., "hapeko" from "www.hapeko.de")
                const parts = domain.split('.');
                if (parts.length >= 2) {
                    // Return the second-to-last part (main domain name)
                    return parts[parts.length - 2];
                }
                return domain;
            } catch (e) {
                return 'unknown';
            }
        }

        // Display jobs
        function displayJobs(jobs, filteredSource = '') {
            console.log('displayJobs() called with', jobs.length, 'jobs');
            const container = document.getElementById('jobs-container');

            if (jobs.length === 0) {
                console.log('No jobs to display');
                container.innerHTML = '<p>No jobs found. Try scraping some jobs first.</p>';
                return;
            }

            // Store jobs globally for filtering
            allJobs = jobs;

            // Get source mapping from global jobUrls and extract domain names
            const sourceMap = {};
            const domainMap = {};
            if (window.jobUrls) {
                window.jobUrls.forEach(urlData => {
                    const domainName = extractDomainName(urlData.url);
                    sourceMap[urlData.prefix] = domainName;
                    domainMap[urlData.prefix] = urlData.url;
                });
            }

            // Populate source filter dropdown
            populateSourceFilter(jobs, sourceMap);

            // Filter jobs if a source is selected
            let jobsToDisplay = jobs;
            if (filteredSource) {
                jobsToDisplay = jobs.filter(job => {
                    const jobDomain = sourceMap[job.source] || job.source;
                    return jobDomain === filteredSource;
                });
            }

            // Update job count display
            updateJobCountDisplay(jobsToDisplay.length, jobs.length, filteredSource);

            const jobsHtml = jobsToDisplay.map(job => {
                const domainName = sourceMap[job.source] || job.source || 'Unknown';
                // Format the scraped_at date
                let scrapedDate = 'Unknown';
                if (job.scraped_at) {
                    try {
                        const d = new Date(job.scraped_at);
                        scrapedDate = d.toLocaleDateString() + ' ' + d.toLocaleTimeString();
                    } catch (e) {
                        scrapedDate = job.scraped_at;
                    }
                }
                return `
                <div class="job-item" data-source="${domainName}">
                    <h3>${job.title || 'No Title'}</h3>
                    <p><strong>Company:</strong> ${job.company || 'Unknown'}</p>
                    <p><strong>Location:</strong> ${job.location || 'Not specified'}</p>
                    <p><strong>Source:</strong> <span class="job-source">${domainName}</span></p>
                    <p><strong>URL:</strong> <a href="${job.url || job.link}" target="_blank">View Job</a></p>
                    <p><strong>Scraped on:</strong> ${scrapedDate}</p>
                    ${job.description ? `<p><strong>Description:</strong> ${job.description.substring(0, 200)}...</p>` : ''}
                </div>
                `;
            }).join('');

            container.innerHTML = `<div class="grid">${jobsHtml}</div>`;
        }

        // Populate source filter dropdown
        function populateSourceFilter(jobs, sourceMap) {
            const sourceFilter = document.getElementById('source-filter');
            const sources = new Set();

            jobs.forEach(job => {
                const domainName = sourceMap[job.source] || job.source || 'Unknown';
                sources.add(domainName);
            });

            // Clear existing options except "All Sources"
            sourceFilter.innerHTML = '<option value="">All Sources</option>';

            // Add source options sorted alphabetically
            Array.from(sources).sort().forEach(source => {
                const option = document.createElement('option');
                option.value = source;
                option.textContent = source;
                sourceFilter.appendChild(option);
            });
        }

        // Update job count display
        function updateJobCountDisplay(displayedCount, totalCount, filteredSource) {
            const countDisplay = document.getElementById('job-count-display');
            if (filteredSource) {
                countDisplay.textContent = `Showing ${displayedCount} of ${totalCount} jobs (filtered by: ${filteredSource})`;
            } else {
                countDisplay.textContent = `Showing ${displayedCount} jobs`;
            }
        }

        // Filter jobs by source
        function filterJobsBySource() {
            const sourceFilter = document.getElementById('source-filter');
            const selectedSource = sourceFilter.value;
            displayJobs(allJobs, selectedSource);
        }

        // Load CV summary
        async function loadCvSummary() {
            try {
                const response = await fetch(`${API_BASE}/api/cv`);
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('cv-summary').value = data.summary || '';
                }
            } catch (error) {
                console.error('Error loading CV summary:', error);
            }
        }

        // Load comprehensive analysis results on page load
        async function loadComprehensiveAnalysis() {
            try {
                // Get current temperature setting from UI (correct element ID is 'min-score')
                const temp = parseFloat(document.getElementById('min-score').value) || 0.7;
                // Use 0.0 as min_confidence for loading to show all cached results
                const minConfidence = 0.0;

                console.log('🔍 Loading comprehensive analysis results...');
                console.log(`📡 API call: ${API_BASE}/api/jobs/comprehensive-analysis?temp=${temp}&min_confidence=${minConfidence}`);
                const response = await fetch(`${API_BASE}/api/jobs/comprehensive-analysis?temp=${temp}&min_confidence=${minConfidence}`);

                if (response.ok) {
                    const data = await response.json();
                    const analyzedJobs = data.analyzed_jobs || [];

                    if (analyzedJobs.length > 0) {
                        console.log(`✅ Loaded ${analyzedJobs.length} cached comprehensive analysis results`);

                        // Display the comprehensive analysis results
                        displayComprehensiveAnalysis(analyzedJobs);

                        // Show success message
                        showMessage('shortlist-message',
                            `✅ Loaded ${analyzedJobs.length} cached comprehensive analysis results from previous session`,
                            'success');

                        // Make sure the comprehensive button is visible
                        const comprehensiveBtn = document.getElementById('comprehensive-btn');
                        if (comprehensiveBtn) {
                            comprehensiveBtn.style.display = 'inline-block';
                            console.log('Comprehensive button made visible from cached results');
                        }

                        return true; // Indicate that comprehensive analysis was loaded
                    } else {
                        console.log('No cached comprehensive analysis results found');
                        return false;
                    }
                } else {
                    console.log('❌ Failed to load comprehensive analysis results:', response.status);
                    const errorText = await response.text().catch(() => 'Unknown error');
                    console.log('❌ Error response:', errorText);
                    return false;
                }
            } catch (error) {
                console.error('❌ Error loading comprehensive analysis results:', error);
                return false;
            }
        }

        // Show message helper
        function showMessage(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}-message">${message}</div>`;
            setTimeout(() => {
                element.innerHTML = '';
            }, 5000);
        }

        // Initialize - Single DOMContentLoaded listener
        document.addEventListener('DOMContentLoaded', async function() {
            checkBackendHealth();
            loadJobUrls();
            loadCvSummary();
            loadScrapingStats();

            // Load shortlist status and comprehensive analysis in sequence
            await checkCVChangeStatus();

            // Try to load comprehensive analysis results after shortlist
            // This ensures we display both shortlisted jobs AND comprehensive analysis if available
            setTimeout(async () => {
                const hasComprehensiveResults = await loadComprehensiveAnalysis();
                if (hasComprehensiveResults) {
                    console.log('✅ Comprehensive analysis results loaded and displayed');
                } else {
                    console.log('ℹ️ No comprehensive analysis results to display');
                }
            }, 500);

            // Auto-load jobs after a short delay to ensure backend is ready
            setTimeout(() => {
                console.log('Auto-loading jobs...');
                loadJobs();
            }, 1000);
        });

        // ===== JOB STATISTICS & CATEGORIZATION FUNCTIONALITY =====

        let categoryChart = null;
        let clusterChart = null;
        let currentCategoryData = null;
        let currentClusterData = null;
        let filteredClusterData = null;

        // Color palette for categories
        const categoryColors = [
            '#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe',
            '#43e97b', '#38f9d7', '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3',
            '#ff9a9e', '#fecfef', '#ffeaa7', '#fab1a0', '#fd79a8', '#fdcb6e',
            '#6c5ce7', '#a29bfe', '#fd79a8', '#e17055', '#00b894', '#00cec9'
        ];

        // Load job statistics and display visualization
        async function loadJobStatistics() {
            const loadBtn = document.getElementById('load-stats-btn');
            const refreshBtn = document.getElementById('refresh-stats-btn');
            const container = document.getElementById('statistics-container');
            const loading = document.getElementById('stats-loading');
            const message = document.getElementById('stats-message');
            const summary = document.getElementById('stats-summary');

            try {
                // Show loading state
                loadBtn.disabled = true;
                refreshBtn.disabled = true;
                loading.style.display = 'block';
                container.style.display = 'none';
                message.innerHTML = '';

                console.log('🔄 Loading job statistics from:', `${API_BASE}/api/jobs/categories`);

                const response = await fetch(`${API_BASE}/api/jobs/categories`);

                if (response.ok) {
                    const data = await response.json();

                    if (data.success) {
                        currentCategoryData = data;

                        // Update summary
                        summary.textContent = `Total: ${data.total_jobs} jobs across ${data.categories.length} categories`;

                        // Create/update chart
                        createCategoryChart(data.categories);

                        // Create category legend and filters
                        createCategoryLegend(data.categories);

                        // Create detailed statistics table
                        createCategoryTable(data.categories);

                        // Show container
                        container.style.display = 'block';

                        showMessage('stats-message', `✅ Loaded statistics for ${data.total_jobs} jobs!`, 'success');
                    } else {
                        throw new Error(data.message || 'Failed to load statistics');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: Failed to load statistics`);
                }

            } catch (error) {
                console.error('❌ Error loading statistics:', error);
                showMessage('stats-message', 'Error loading statistics: ' + error.message, 'error');
            } finally {
                loadBtn.disabled = false;
                refreshBtn.disabled = false;
                loading.style.display = 'none';
            }
        }

        // Refresh job statistics
        async function refreshJobStatistics() {
            await loadJobStatistics();
        }

        // Create or update the category chart
        function createCategoryChart(categories) {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            const chartType = document.getElementById('chart-type').value;

            // Destroy existing chart if it exists
            if (categoryChart) {
                categoryChart.destroy();
            }

            // Prepare data
            const labels = categories.map(cat => cat.category);
            const data = categories.map(cat => cat.count);
            const colors = categoryColors.slice(0, categories.length);

            // Chart configuration
            const config = {
                type: chartType,
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Job Count',
                        data: data,
                        backgroundColor: colors,
                        borderColor: colors.map(color => color + '80'),
                        borderWidth: 2,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: chartType === 'pie',
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 15,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const category = categories[context.dataIndex];
                                    return `${context.label}: ${context.parsed} jobs (${category.percentage}%)`;
                                }
                            }
                        }
                    },
                    onClick: (event, elements) => {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            const category = categories[index];
                            filterJobsByCategory(category.category);
                        }
                    },
                    scales: chartType === 'bar' ? {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    } : {}
                }
            };

            // Create new chart
            categoryChart = new Chart(ctx, config);
        }

        // Update chart type
        function updateChartType() {
            if (currentCategoryData) {
                createCategoryChart(currentCategoryData.categories);
            }
        }

        // ===== SEPARATE 2D CLUSTER VISUALIZATION FUNCTIONS =====

        // Load cluster visualization
        async function loadClusterVisualization() {
            const loadBtn = document.getElementById('load-cluster-btn');
            const refreshBtn = document.getElementById('refresh-cluster-btn');
            const container = document.getElementById('cluster-container');
            const loading = document.getElementById('cluster-loading');
            const message = document.getElementById('cluster-message');
            const summary = document.getElementById('cluster-summary');

            try {
                // Show loading state
                loadBtn.disabled = true;
                refreshBtn.disabled = true;
                loading.style.display = 'block';
                container.style.display = 'none';
                message.innerHTML = '';

                console.log('🔄 Loading cluster data from:', `${API_BASE}/api/jobs/cluster-data`);

                const response = await fetch(`${API_BASE}/api/jobs/cluster-data`);

                if (response.ok) {
                    const data = await response.json();

                    if (data.success) {
                        currentClusterData = data;
                        filteredClusterData = data; // Initialize filtered data

                        // Update summary
                        summary.textContent = `Total: ${data.total_jobs} jobs visualized in 2D space`;

                        // Populate filter dropdowns
                        populateClusterFilters(data);

                        // Create cluster chart
                        createClusterChart(data);

                        // Create detailed legends
                        createClusterDetailedLegends(data);

                        // Show container
                        container.style.display = 'block';

                        showMessage('cluster-message', `✅ Loaded 2D cluster visualization for ${data.total_jobs} jobs!`, 'success');
                    } else {
                        throw new Error(data.message || 'Failed to load cluster data');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: Failed to load cluster data`);
                }

            } catch (error) {
                console.error('❌ Error loading cluster data:', error);
                showMessage('cluster-message', 'Error loading cluster visualization: ' + error.message, 'error');
            } finally {
                loadBtn.disabled = false;
                refreshBtn.disabled = false;
                loading.style.display = 'none';
            }
        }

        // Refresh cluster visualization
        async function refreshClusterVisualization() {
            await loadClusterVisualization();
        }

        // Create 2D cluster visualization
        function createClusterChart(clusterData) {
            const ctx = document.getElementById('clusterChart').getContext('2d');

            // Destroy existing chart if it exists
            if (clusterChart) {
                clusterChart.destroy();
            }

            // Use filtered data if available
            const dataToUse = filteredClusterData || clusterData;

            // Prepare datasets grouped by seniority level
            const seniority_levels = ['Junior', 'Professional', 'Senior', 'Lead', 'Executive'];
            const datasets = [];

            seniority_levels.forEach(seniority => {
                const points = dataToUse.cluster_data.filter(point => point.seniority === seniority);

                if (points.length > 0) {
                    // Get shape configuration
                    const shapeConfig = clusterData.seniority_config[seniority];
                    let pointStyle = 'circle';

                    // Map shapes to Chart.js point styles
                    switch (shapeConfig.shape) {
                        case 'circle': pointStyle = 'circle'; break;
                        case 'square': pointStyle = 'rect'; break;
                        case 'triangle': pointStyle = 'triangle'; break;
                        case 'diamond': pointStyle = 'rectRot'; break;
                        case 'star': pointStyle = 'star'; break;
                        default: pointStyle = 'circle';
                    }

                    datasets.push({
                        label: seniority,
                        data: points.map(point => ({
                            x: point.x,
                            y: point.y,
                            title: point.title,
                            category: point.category,
                            company: point.company,
                            location: point.location,
                            link: point.link,
                            jobId: point.id
                        })),
                        backgroundColor: points.map(point => point.color),
                        borderColor: points.map(point => point.color),
                        borderWidth: 2,
                        pointRadius: shapeConfig.size / 2,
                        pointHoverRadius: shapeConfig.size / 2 + 2,
                        pointStyle: pointStyle,
                        showLine: false
                    });
                }
            });

            // Chart configuration
            const config = {
                type: 'scatter',
                data: { datasets: datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 12,
                                padding: 15,
                                font: { size: 11 }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    const point = context[0].raw;
                                    return point.title;
                                },
                                label: function(context) {
                                    const point = context[0].raw;
                                    return [
                                        `Category: ${point.category}`,
                                        `Seniority: ${context.dataset.label}`,
                                        `Company: ${point.company}`,
                                        `Location: ${point.location}`
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: 'UMAP Dimension 1'
                            },
                            min: 0,
                            max: 11,
                            grid: {
                                color: '#e0e0e0'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'UMAP Dimension 2'
                            },
                            min: 0,
                            max: 11,
                            grid: {
                                color: '#e0e0e0'
                            }
                        }
                    },
                    onClick: (event, elements) => {
                        if (elements.length > 0) {
                            const element = elements[0];
                            const point = element.element.$context.raw;
                            if (point.link) {
                                window.open(point.link, '_blank');
                            }
                        }
                    }
                }
            };

            // Create new chart
            clusterChart = new Chart(ctx, config);
        }

        // Populate cluster filter dropdowns
        function populateClusterFilters(clusterData) {
            const seniorityFilter = document.getElementById('seniority-filter');
            const categoryFilter = document.getElementById('cluster-category-filter');

            // Clear existing options
            seniorityFilter.innerHTML = '<option value="">All Seniority Levels</option>';
            categoryFilter.innerHTML = '<option value="">All Categories</option>';

            // Add seniority options
            Object.entries(clusterData.seniority_counts).forEach(([level, count]) => {
                if (count > 0) {
                    const option = document.createElement('option');
                    option.value = level;
                    option.textContent = `${level} (${count})`;
                    seniorityFilter.appendChild(option);
                }
            });

            // Add category options
            Object.entries(clusterData.category_counts).forEach(([category, count]) => {
                if (count > 0) {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = `${category} (${count})`;
                    categoryFilter.appendChild(option);
                }
            });
        }

        // Create detailed cluster legends
        function createClusterDetailedLegends(clusterData) {
            const categoryLegendContainer = document.getElementById('cluster-category-legend-detailed');
            const seniorityLegendContainer = document.getElementById('cluster-seniority-legend-detailed');

            // Create category legend (colors)
            let categoryHTML = '';
            Object.entries(clusterData.category_colors).forEach(([category, color]) => {
                const count = clusterData.category_counts[category] || 0;
                if (count > 0) {
                    categoryHTML += `
                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 8px 12px; background: #f8f9fa; border-radius: 6px; cursor: pointer;"
                             onclick="filterClusterByCategory('${category}')"
                             onmouseover="this.style.backgroundColor='#e9ecef'"
                             onmouseout="this.style.backgroundColor='#f8f9fa'">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div style="width: 16px; height: 16px; background: ${color}; border-radius: 3px;"></div>
                                <span style="font-size: 0.85em; font-weight: 500;">${category}</span>
                            </div>
                            <span style="background: ${color}; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.75em; font-weight: bold;">${count}</span>
                        </div>
                    `;
                }
            });
            categoryLegendContainer.innerHTML = categoryHTML;

            // Create seniority legend (shapes)
            let seniorityHTML = '';
            Object.entries(clusterData.seniority_config).forEach(([level, config]) => {
                const count = clusterData.seniority_counts[level] || 0;
                if (count > 0) {
                    let shapeSymbol = '●'; // Default circle
                    switch (config.shape) {
                        case 'circle': shapeSymbol = '●'; break;
                        case 'square': shapeSymbol = '■'; break;
                        case 'triangle': shapeSymbol = '▲'; break;
                        case 'diamond': shapeSymbol = '♦'; break;
                        case 'star': shapeSymbol = '★'; break;
                    }

                    seniorityHTML += `
                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 8px 12px; background: #f8f9fa; border-radius: 6px; cursor: pointer;"
                             onclick="filterClusterBySeniority('${level}')"
                             onmouseover="this.style.backgroundColor='#e9ecef'"
                             onmouseout="this.style.backgroundColor='#f8f9fa'">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span style="font-size: ${config.size + 2}px; color: #666;">${shapeSymbol}</span>
                                <span style="font-size: 0.85em; font-weight: 500;">${level}</span>
                            </div>
                            <span style="background: #666; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.75em; font-weight: bold;">${count}</span>
                        </div>
                    `;
                }
            });
            seniorityLegendContainer.innerHTML = seniorityHTML;
        }

        // Filter cluster by seniority
        function filterClusterBySeniority(selectedSeniority = null) {
            if (!currentClusterData) return;

            const seniorityFilter = document.getElementById('seniority-filter');
            const categoryFilter = document.getElementById('cluster-category-filter');
            const summary = document.getElementById('cluster-summary');

            // If selectedSeniority is provided, update the dropdown
            if (selectedSeniority) {
                seniorityFilter.value = selectedSeniority;
            }

            const seniorityValue = selectedSeniority || seniorityFilter.value;
            const categoryValue = categoryFilter.value;

            // Filter data
            let filteredData = { ...currentClusterData };
            filteredData.cluster_data = currentClusterData.cluster_data.filter(point => {
                const seniorityMatch = !seniorityValue || point.seniority === seniorityValue;
                const categoryMatch = !categoryValue || point.category === categoryValue;
                return seniorityMatch && categoryMatch;
            });

            // Update filtered data
            filteredClusterData = filteredData;

            // Update summary
            const totalFiltered = filteredData.cluster_data.length;
            let filterText = '';
            if (seniorityValue && categoryValue) {
                filterText = ` (filtered by ${seniorityValue} & ${categoryValue})`;
            } else if (seniorityValue) {
                filterText = ` (filtered by ${seniorityValue})`;
            } else if (categoryValue) {
                filterText = ` (filtered by ${categoryValue})`;
            }
            summary.textContent = `Showing: ${totalFiltered} jobs${filterText}`;

            // Recreate chart with filtered data
            createClusterChart(currentClusterData);
        }

        // Filter cluster by category
        function filterClusterByCategory(selectedCategory = null) {
            if (!currentClusterData) return;

            const seniorityFilter = document.getElementById('seniority-filter');
            const categoryFilter = document.getElementById('cluster-category-filter');
            const summary = document.getElementById('cluster-summary');

            // If selectedCategory is provided, update the dropdown
            if (selectedCategory) {
                categoryFilter.value = selectedCategory;
            }

            const seniorityValue = seniorityFilter.value;
            const categoryValue = selectedCategory || categoryFilter.value;

            // Filter data
            let filteredData = { ...currentClusterData };
            filteredData.cluster_data = currentClusterData.cluster_data.filter(point => {
                const seniorityMatch = !seniorityValue || point.seniority === seniorityValue;
                const categoryMatch = !categoryValue || point.category === categoryValue;
                return seniorityMatch && categoryMatch;
            });

            // Update filtered data
            filteredClusterData = filteredData;

            // Update summary
            const totalFiltered = filteredData.cluster_data.length;
            let filterText = '';
            if (seniorityValue && categoryValue) {
                filterText = ` (filtered by ${seniorityValue} & ${categoryValue})`;
            } else if (seniorityValue) {
                filterText = ` (filtered by ${seniorityValue})`;
            } else if (categoryValue) {
                filterText = ` (filtered by ${categoryValue})`;
            }
            summary.textContent = `Showing: ${totalFiltered} jobs${filterText}`;

            // Recreate chart with filtered data
            createClusterChart(currentClusterData);
        }

        // Create category legend with filter buttons
        function createCategoryLegend(categories) {
            const legendContainer = document.getElementById('category-legend');

            let legendHTML = '<div style="margin-bottom: 15px;">';
            legendHTML += '<button class="btn" onclick="clearCategoryFilter()" style="font-size: 0.8em; padding: 6px 12px; margin-bottom: 10px;">Show All Jobs</button>';
            legendHTML += '</div>';

            categories.forEach((category, index) => {
                const color = categoryColors[index % categoryColors.length];
                const percentage = category.percentage;

                legendHTML += `
                    <div class="category-legend-item" style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 8px 12px;
                        margin-bottom: 8px;
                        background: #fff;
                        border-radius: 6px;
                        border-left: 4px solid ${color};
                        cursor: pointer;
                        transition: all 0.2s ease;
                        border: 1px solid #e0e0e0;
                    " onclick="filterJobsByCategory('${category.category}')"
                       onmouseover="this.style.backgroundColor='#f8f9fa'; this.style.transform='translateX(2px)'"
                       onmouseout="this.style.backgroundColor='#fff'; this.style.transform='translateX(0)'">
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: #333; font-size: 0.9em; margin-bottom: 2px;">
                                ${category.category}
                            </div>
                            <div style="font-size: 0.8em; color: #666;">
                                ${category.count} jobs (${percentage}%)
                            </div>
                        </div>
                        <div style="
                            background: ${color};
                            color: white;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 0.8em;
                            font-weight: bold;
                        ">
                            ${category.count}
                        </div>
                    </div>
                `;
            });

            legendContainer.innerHTML = legendHTML;
        }

        // Create detailed statistics table
        function createCategoryTable(categories) {
            const tableBody = document.getElementById('category-table-body');

            let tableHTML = '';
            categories.forEach((category, index) => {
                const color = categoryColors[index % categoryColors.length];

                tableHTML += `
                    <tr style="border-bottom: 1px solid #f0f0f0;">
                        <td style="padding: 12px;">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="width: 16px; height: 16px; background: ${color}; border-radius: 3px;"></div>
                                <span style="font-weight: 500;">${category.category}</span>
                            </div>
                        </td>
                        <td style="padding: 12px; text-align: center; font-weight: bold; color: #333;">
                            ${category.count}
                        </td>
                        <td style="padding: 12px; text-align: center;">
                            <span style="background: ${color}20; color: ${color}; padding: 4px 8px; border-radius: 12px; font-size: 0.9em; font-weight: bold;">
                                ${category.percentage}%
                            </span>
                        </td>
                        <td style="padding: 12px; text-align: center;">
                            <button onclick="filterJobsByCategory('${category.category}')"
                                    style="background: ${color}; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 0.8em;"
                                    onmouseover="this.style.opacity='0.8'"
                                    onmouseout="this.style.opacity='1'">
                                View Jobs
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableBody.innerHTML = tableHTML;
        }

        // Filter jobs by category and update job listings
        async function filterJobsByCategory(category) {
            try {
                console.log(`🔍 Filtering jobs by category: ${category}`);

                // Show loading message
                showMessage('stats-message', `Loading jobs in category: ${category}...`, 'info');

                // Fetch jobs for this category
                const response = await fetch(`${API_BASE}/api/jobs/by-category/${encodeURIComponent(category)}`);

                if (response.ok) {
                    const data = await response.json();

                    if (data.success) {
                        // Update the job listings section with filtered jobs
                        displayFilteredJobs(data.jobs, category);

                        // Scroll to job listings section
                        document.querySelector('#jobs-container').scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });

                        showMessage('stats-message', `✅ Showing ${data.count} jobs in category: ${category}`, 'success');
                    } else {
                        throw new Error(data.message || 'Failed to filter jobs');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: Failed to filter jobs`);
                }

            } catch (error) {
                console.error('❌ Error filtering jobs:', error);
                showMessage('stats-message', 'Error filtering jobs: ' + error.message, 'error');
            }
        }

        // Clear category filter and show all jobs
        function clearCategoryFilter() {
            console.log('🔄 Clearing category filter');
            loadJobs(); // Load all jobs
            showMessage('stats-message', '✅ Showing all jobs', 'success');
        }

        // Display filtered jobs in the job listings section
        function displayFilteredJobs(jobs, category) {
            const container = document.getElementById('jobs-container');
            const countDisplay = document.getElementById('job-count-display');
            const sourceFilter = document.getElementById('source-filter');

            // Update count display
            countDisplay.textContent = `Showing ${jobs.length} jobs in category: ${category}`;

            // Reset source filter
            sourceFilter.value = '';

            if (jobs.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <h3>No jobs found in category: ${category}</h3>
                        <p>Try selecting a different category or <button onclick="clearCategoryFilter()" class="btn" style="display: inline; margin: 0 5px;">view all jobs</button></p>
                    </div>
                `;
                return;
            }

            // Create job listings HTML
            let jobsHTML = `
                <div style="margin-bottom: 15px; padding: 10px; background: #e3f2fd; border-radius: 8px; border-left: 4px solid #2196f3;">
                    <strong>📂 Category Filter Active:</strong> ${category}
                    <button onclick="clearCategoryFilter()" style="margin-left: 10px; background: #2196f3; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 0.8em;">
                        Clear Filter
                    </button>
                </div>
                <div class="grid">
            `;

            jobs.forEach(job => {
                // Get source display name
                const sourceDisplayName = getSourceDisplayName(job.source);

                jobsHTML += `
                    <div class="job-item">
                        <h3>${job.title}</h3>
                        <p><strong>Company:</strong> ${job.company}</p>
                        <p><strong>Location:</strong> ${job.location}</p>
                        <p><strong>Description:</strong> ${job.description}</p>
                        <p><strong>Category:</strong> <span style="background: #667eea; color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.8em;">${job.functional_category || category}</span></p>
                        <p><strong>Source:</strong> <span class="job-source">${sourceDisplayName}</span></p>
                        <p><strong>Link:</strong> <a href="${job.link}" target="_blank" style="color: #667eea;">View Job</a></p>
                        ${job.isShortlisted ? '<p style="color: #4caf50; font-weight: bold;">⭐ Shortlisted</p>' : ''}
                    </div>
                `;
            });

            jobsHTML += '</div>';
            container.innerHTML = jobsHTML;
        }

        // Helper function to get source display name (reuse existing logic)
        function getSourceDisplayName(sourcePrefix) {
            if (!window.jobUrls) return sourcePrefix;

            const urlData = window.jobUrls.find(u => u.prefix === sourcePrefix);
            if (urlData && urlData.url) {
                try {
                    const domain = new URL(urlData.url).hostname;
                    return domain.replace('www.', '');
                } catch (e) {
                    return sourcePrefix;
                }
            }
            return sourcePrefix;
        }

        // Filter jobs by selected category from dropdown
        function filterJobsBySelectedCategory() {
            const categoryFilter = document.getElementById('category-filter');
            const selectedCategory = categoryFilter.value;

            if (selectedCategory) {
                filterJobsByCategory(selectedCategory);
            } else {
                clearCategoryFilter();
            }
        }

        // Populate category filter dropdown
        function populateCategoryFilter(categories) {
            const categoryFilter = document.getElementById('category-filter');

            // Clear existing options except "All Categories"
            categoryFilter.innerHTML = '<option value="">All Categories</option>';

            // Add category options
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.category;
                option.textContent = `${category.category} (${category.count})`;
                categoryFilter.appendChild(option);
            });
        }

        // Enhanced loadJobs function to also populate category filter
        const originalLoadJobs = loadJobs;
        loadJobs = async function() {
            // Call original loadJobs function
            await originalLoadJobs();

            // Also load and populate category filter
            try {
                const response = await fetch(`${API_BASE}/api/jobs/categories`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        populateCategoryFilter(data.categories);
                    }
                }
            } catch (error) {
                console.log('Could not load categories for filter:', error);
            }
        };
    </script>
</body>
</html>
