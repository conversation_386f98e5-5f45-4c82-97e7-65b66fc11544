#!/usr/bin/env python3
"""
Debug script to examine the actual Firecrawl cache data for PSP.de
and see what content is being returned from the crawl.
"""

import os
import json
import logging
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def debug_firecrawl_cache():
    """Debug the Firecrawl cache to see what's actually stored."""
    
    print("=" * 80)
    print("🔍 DEBUGGING FIRECRAWL CACHE FOR PSP.DE")
    print("=" * 80)
    
    # Check if Firecrawl API key exists
    firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
    if not firecrawl_api_key:
        print("❌ FIRECRAWL_API_KEY not found in environment")
        return
    
    try:
        from firecrawl import FirecrawlApp
        firecrawl = FirecrawlApp(api_key=firecrawl_api_key)
        
        # Check the cache file
        cache_file = 'data/firecrawl_cache.json'
        if not os.path.exists(cache_file):
            print(f"❌ Cache file {cache_file} not found")
            return
        
        print(f"📁 Reading cache file: {cache_file}")
        with open(cache_file, 'r', encoding='utf-8') as f:
            cache_data = json.load(f)
        
        # Find PSP.de entries
        psp_url = "https://www.psp.de/stellenausschreibungen.html"
        psp_entries = []
        
        for url, crawl_info in cache_data.items():
            if 'psp.de' in url:
                psp_entries.append((url, crawl_info))
        
        if not psp_entries:
            print(f"❌ No PSP.de entries found in cache")
            return
        
        print(f"✅ Found {len(psp_entries)} PSP.de entries in cache:")
        
        for url, crawl_info in psp_entries:
            print(f"\n📄 URL: {url}")
            print(f"   Crawl ID: {crawl_info.get('crawl_id', 'N/A')}")
            print(f"   Timestamp: {crawl_info.get('timestamp', 'N/A')}")
            
            crawl_id = crawl_info.get('crawl_id')
            if not crawl_id:
                print(f"   ❌ No crawl ID found")
                continue
            
            # Get the actual crawl results from Firecrawl
            print(f"   🔍 Checking crawl status for {crawl_id[:8]}...")
            
            try:
                crawl_status = firecrawl.check_crawl_status(crawl_id)
                
                if not crawl_status:
                    print(f"   ❌ Failed to get crawl status")
                    continue
                
                print(f"   📊 Status: {crawl_status.status}")
                print(f"   📄 Total pages: {len(crawl_status.data) if crawl_status.data else 0}")
                
                if crawl_status.status == 'completed' and crawl_status.data:
                    print(f"   🔍 Analyzing page content...")
                    
                    for i, page in enumerate(crawl_status.data[:5]):  # Check first 5 pages
                        print(f"\n      📄 Page {i+1}:")
                        
                        # Handle different page data structures
                        page_markdown = None
                        page_url = url
                        
                        if isinstance(page, dict):
                            page_markdown = page.get('markdown')
                            if page.get('metadata') and isinstance(page['metadata'], dict):
                                page_url = page['metadata'].get('sourceURL', url)
                            print(f"         URL: {page_url}")
                            print(f"         Keys: {list(page.keys())}")
                            print(f"         Markdown length: {len(page_markdown) if page_markdown else 0}")
                            
                            if page_markdown:
                                # Show first 200 characters
                                preview = page_markdown[:200].replace('\n', '\\n')
                                print(f"         Preview: {preview}...")
                                
                                # Check for job-related keywords
                                job_keywords = ['job', 'position', 'stelle', 'karriere', 'bewerbung', 'vacancy']
                                found_keywords = [kw for kw in job_keywords if kw.lower() in page_markdown.lower()]
                                print(f"         Job keywords found: {found_keywords}")
                            else:
                                print(f"         ❌ No markdown content")
                        
                        elif hasattr(page, 'markdown'):
                            page_markdown = page.markdown
                            if hasattr(page, 'metadata'):
                                if hasattr(page.metadata, 'sourceURL'):
                                    page_url = page.metadata.sourceURL
                                elif isinstance(page.metadata, dict):
                                    page_url = page.metadata.get('sourceURL', url)
                            
                            print(f"         URL: {page_url}")
                            print(f"         Markdown length: {len(page_markdown) if page_markdown else 0}")
                            
                            if page_markdown:
                                preview = page_markdown[:200].replace('\n', '\\n')
                                print(f"         Preview: {preview}...")
                            else:
                                print(f"         ❌ No markdown content")
                        else:
                            print(f"         ❌ Unknown page structure: {type(page)}")
                            print(f"         Attributes: {[attr for attr in dir(page) if not attr.startswith('_')]}")
                    
                    # Save detailed analysis
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    debug_file = f"debug_firecrawl_pages_{timestamp}.json"
                    
                    # Convert pages to serializable format
                    serializable_pages = []
                    for page in crawl_status.data:
                        if isinstance(page, dict):
                            serializable_pages.append(page)
                        else:
                            # Convert object to dict
                            page_dict = {}
                            if hasattr(page, 'markdown'):
                                page_dict['markdown'] = page.markdown
                            if hasattr(page, 'metadata'):
                                if hasattr(page.metadata, 'sourceURL'):
                                    page_dict['metadata'] = {'sourceURL': page.metadata.sourceURL}
                                elif isinstance(page.metadata, dict):
                                    page_dict['metadata'] = page.metadata
                            serializable_pages.append(page_dict)
                    
                    with open(debug_file, 'w', encoding='utf-8') as f:
                        json.dump({
                            'crawl_id': crawl_id,
                            'status': crawl_status.status,
                            'total_pages': len(crawl_status.data),
                            'pages': serializable_pages
                        }, f, indent=2, ensure_ascii=False)
                    
                    print(f"\n   💾 Detailed page data saved to: {debug_file}")
                
                else:
                    print(f"   ❌ Crawl not completed or no data (status: {crawl_status.status})")
                    
            except Exception as e:
                print(f"   ❌ Error checking crawl status: {e}")
    
    except Exception as e:
        print(f"❌ Error debugging Firecrawl cache: {e}")
    
    print(f"\n" + "=" * 80)
    print("🏁 FIRECRAWL CACHE DEBUG COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    debug_firecrawl_cache()
