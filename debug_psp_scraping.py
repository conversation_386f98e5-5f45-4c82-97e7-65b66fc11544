#!/usr/bin/env python3
"""
Debug script to analyze why PSP.de jobs are not being extracted.
This script will test the scraping process step by step for PSP.de specifically.
"""

import os
import json
import logging
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_psp_scraping():
    """Analyze the PSP.de scraping process step by step."""
    
    print("=" * 80)
    print("🔍 PSP.DE SCRAPING ANALYSIS")
    print("=" * 80)
    
    # 1. Check if PSP.de is in the URL configuration
    print("\n1. CHECKING URL CONFIGURATION:")
    try:
        with open('data/job_urls.json', 'r', encoding='utf-8') as f:
            urls_data = json.load(f)
        
        psp_url = None
        for url_entry in urls_data:
            if 'psp.de' in url_entry.get('url', ''):
                psp_url = url_entry
                break
        
        if psp_url:
            print(f"   ✅ PSP.de found in configuration:")
            print(f"      URL: {psp_url['url']}")
            print(f"      Prefix: {psp_url['prefix']}")
            print(f"      Active: {psp_url['is_active']}")
            print(f"      Include in scraping: {psp_url['include_in_scraping']}")
            print(f"      Last scraped: {psp_url.get('last_scraped', 'Never')}")
        else:
            print("   ❌ PSP.de not found in URL configuration")
            return
            
    except Exception as e:
        print(f"   ❌ Error reading URL configuration: {e}")
        return
    
    # 2. Check if any jobs exist with PSP prefix
    print(f"\n2. CHECKING FOR JOBS WITH PREFIX '{psp_url['prefix']}':")
    try:
        with open('data/jobs.json', 'r', encoding='utf-8') as f:
            jobs_data = json.load(f)
        
        psp_jobs = [job for job in jobs_data if job.get('source') == psp_url['prefix']]
        
        if psp_jobs:
            print(f"   ✅ Found {len(psp_jobs)} jobs with PSP prefix:")
            for job in psp_jobs[:5]:  # Show first 5
                print(f"      - {job['id']}: {job['title']}")
        else:
            print(f"   ❌ No jobs found with PSP prefix '{psp_url['prefix']}'")
            
    except Exception as e:
        print(f"   ❌ Error reading jobs data: {e}")
    
    # 3. Test Firecrawl access to PSP.de
    print(f"\n3. TESTING FIRECRAWL ACCESS TO PSP.DE:")
    try:
        from firecrawl import FirecrawlApp
        
        firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
        if not firecrawl_api_key:
            print("   ❌ FIRECRAWL_API_KEY not found in environment")
            return
            
        firecrawl = FirecrawlApp(api_key=firecrawl_api_key)
        
        # Test scraping the PSP.de page
        print(f"   🔍 Attempting to scrape: {psp_url['url']}")
        
        # Use the simple Firecrawl API format
        scrape_result = firecrawl.scrape_url(psp_url['url'])

        if hasattr(scrape_result, 'success') and scrape_result.success:
            content = scrape_result.data.get('markdown', '') if hasattr(scrape_result, 'data') else ''
            print(f"   ✅ Successfully scraped PSP.de")
            print(f"      Content length: {len(content)} characters")

            # Debug: show what data we actually got
            if hasattr(scrape_result, 'data'):
                print(f"      Data keys: {list(scrape_result.data.keys()) if scrape_result.data else 'No data'}")
                if scrape_result.data:
                    for key, value in scrape_result.data.items():
                        if isinstance(value, str):
                            print(f"      {key}: {len(value)} chars - '{value[:100]}...'")
                        else:
                            print(f"      {key}: {type(value)} - {value}")
            else:
                print(f"      No data attribute found")
            
            # Show a preview of the content
            print(f"   📄 Content preview (first 500 chars):")
            print(f"      {content[:500]}...")
            
            # Look for job-related keywords
            job_keywords = ['stellenausschreibung', 'position', 'job', 'karriere', 'bewerbung', 'stelle']
            found_keywords = []
            content_lower = content.lower()
            
            for keyword in job_keywords:
                if keyword in content_lower:
                    count = content_lower.count(keyword)
                    found_keywords.append(f"{keyword} ({count}x)")
            
            if found_keywords:
                print(f"   🔍 Job-related keywords found: {', '.join(found_keywords)}")
            else:
                print(f"   ⚠️  No obvious job-related keywords found")
                
            # Save the scraped content for manual inspection
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            debug_file = f"debug_psp_content_{timestamp}.md"
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(f"# PSP.de Scraped Content - {timestamp}\n\n")
                f.write(f"URL: {psp_url['url']}\n\n")
                f.write(content)
            print(f"   💾 Full content saved to: {debug_file}")
            
        else:
            print(f"   ❌ Failed to scrape PSP.de")
            if hasattr(scrape_result, 'success'):
                print(f"      Success: {scrape_result.success}")
            if hasattr(scrape_result, 'error'):
                print(f"      Error: {scrape_result.error}")
            print(f"      Response type: {type(scrape_result)}")
            print(f"      Response attributes: {[attr for attr in dir(scrape_result) if not attr.startswith('_')]}")
            
    except Exception as e:
        print(f"   ❌ Error testing Firecrawl access: {e}")
    
    # 4. Test LLM job extraction on PSP content
    print(f"\n4. TESTING LLM JOB EXTRACTION:")
    try:
        if 'content' in locals() and content:
            import openai
            
            openai_api_key = os.getenv('OPENAI_API_KEY')
            if not openai_api_key:
                print("   ❌ OPENAI_API_KEY not found in environment")
                return
                
            client = openai.OpenAI(api_key=openai_api_key)
            
            # Use the same prompt as the scraper
            prompt = f"""
            Extract ONLY actual job postings from this content. Focus on complete job descriptions with detailed information.

            IMPORTANT RULES:
            1. Only extract jobs that have DETAILED descriptions, requirements, or responsibilities
            2. Ignore navigation menus, breadcrumbs, image alt text, and lists of job titles without details
            3. Ignore content that appears to be just job title lists or category navigation
            4. Each job must have substantial content beyond just a title and location
            5. If you find multiple jobs on the same page, each must have its own unique detail URL
            6. If jobs share the same URL, only extract the MAIN job posting from that page

            Look for these indicators of a REAL job posting:
            - Detailed job description or responsibilities section
            - Requirements or qualifications section
            - Salary information or benefits
            - Application instructions or contact information
            - Company description related to the specific role

            For each ACTUAL job posting found, return:
            {{
                "title": "exact job title from the main job posting",
                "company": "company name if found, otherwise 'Not specified'",
                "location": "location if found, otherwise 'Not specified'",
                "summary": "brief summary from actual job description (max 150 chars)",
                "detail_url": "direct job URL if found, otherwise '{psp_url['url']}'"
            }}

            Return ONLY a valid JSON array. If you only find one main job posting, return an array with one job.
            Do NOT extract jobs from navigation elements, image alt text, or title lists.

            Content:
            {content[:8000]}
            """
            
            print("   🤖 Sending content to LLM for job extraction...")
            
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a job listing extraction expert. You MUST return valid JSON array format. Extract ALL job postings from the content, even if information is incomplete."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,
                temperature=0.0
            )
            
            response_text = response.choices[0].message.content.strip()
            print(f"   📝 LLM Response length: {len(response_text)} characters")
            print(f"   📄 LLM Response preview:")
            print(f"      {response_text[:500]}...")
            
            # Try to parse the JSON response
            try:
                import re
                
                # First try: direct JSON parsing
                jobs_data = json.loads(response_text)
                print(f"   ✅ Successfully parsed JSON: {len(jobs_data)} jobs found")
                
                for i, job in enumerate(jobs_data):
                    print(f"      Job {i+1}: {job.get('title', 'No title')}")
                    print(f"         Company: {job.get('company', 'Not specified')}")
                    print(f"         Location: {job.get('location', 'Not specified')}")
                    print(f"         Summary: {job.get('summary', 'No summary')[:100]}...")
                    
            except json.JSONDecodeError:
                try:
                    # Second try: extract JSON array from response
                    json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
                    if json_match:
                        jobs_data = json.loads(json_match.group())
                        print(f"   ✅ Extracted JSON from response: {len(jobs_data)} jobs found")
                    else:
                        print(f"   ❌ No JSON array found in LLM response")
                        print(f"   📄 Full response: {response_text}")
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON parsing failed: {str(e)}")
                    print(f"   📄 Full response: {response_text}")
                    
        else:
            print("   ❌ No content available for LLM testing")
            
    except Exception as e:
        print(f"   ❌ Error testing LLM extraction: {e}")
    
    print(f"\n" + "=" * 80)
    print("🏁 ANALYSIS COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    analyze_psp_scraping()
