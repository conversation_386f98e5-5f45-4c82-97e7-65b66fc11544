# 📌 Taskbar Setup Instructions

## ✅ Shortcuts Created Successfully!

The PowerShell script has created shortcuts for Jomade MVP in the following locations:

- **Desktop**: `Jomade MVP.lnk`
- **Start Menu**: Programs → `Jomade MVP.lnk`

## 🎯 How to Pin to Taskbar

### Method 1: From Desktop Shortcut
1. **Right-click** on the "Jomade MVP" shortcut on your desktop
2. Select **"Pin to taskbar"** from the context menu
3. The Jomade MVP icon will appear in your taskbar

### Method 2: From Start Menu
1. Click the **Start button**
2. Type **"Jomade MVP"** to search
3. **Right-click** on "Jomade MVP" in the search results
4. Select **"Pin to taskbar"**

## 🚀 Starting the Application

Once pinned to the taskbar, you can:

1. **Click the taskbar icon** to start Jomade MVP
2. The application will:
   - Check Python installation
   - Install any missing dependencies
   - Start the backend server
   - Display the URL: http://localhost:3000

## 🔧 Alternative Launch Methods

If you prefer not to use the taskbar:

### Direct File Launch
- **Double-click** `launch_jomade.bat` in the project folder

### PowerShell Launch
- **Right-click** `start_jomade.ps1` and select "Run with PowerShell"

### Manual Launch
```bash
# Open Command Prompt or PowerShell in the project folder
pip install -r requirements.txt
python backend/app.py
```

## 🌐 Accessing the Application

Once started, the application will be available at:
- **Main Dashboard**: http://localhost:3000
- **Settings Page**: http://localhost:3000/settings.html

## 🛑 Stopping the Application

To stop Jomade MVP:
1. Go to the command window that opened when you started the app
2. Press **Ctrl+C**
3. The application will stop and the window will close

## 🔍 Troubleshooting

**If the taskbar icon doesn't work:**
1. Make sure Python is installed and in your PATH
2. Try running `launch_jomade.bat` directly first
3. Check that all files are in the correct location

**If you get permission errors:**
1. Run PowerShell as Administrator
2. Execute: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
3. Try creating shortcuts again

---

**🎉 You're all set! Click your taskbar button to start using Jomade MVP!**
