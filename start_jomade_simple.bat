@echo off
title Jomade MVP - Executive Search Application

echo Starting Jomade MVP Application...
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Check if run.ps1 exists
if not exist "run.ps1" (
    echo ERROR: run.ps1 not found
    echo Please run this from the Jomade MVP root directory
    pause
    exit
)

echo Using the official launcher: run.ps1
echo.
echo This will start:
echo   - Backend server: http://localhost:8000
echo   - Frontend server: http://localhost:3000
echo.

REM Run the official PowerShell script
powershell -ExecutionPolicy Bypass -File "run.ps1"

pause
