"""
Weekly archiving utility for Jomade

Behavior (simple, explicit, fail-loudly):
- If called on a Monday (local time), archive the three current data files by
  moving them to data/YYYYMMDD_<name>.json using the PREVIOUS Monday's date.
  (Example: if today is Monday 2025-08-11, we archive to 20250804_<name>.json)
- If called on any other day, do nothing and exit 0 (by user request).
- Also supports a one-off normalization pass to rename old archives that were
  saved without the underscore (e.g., 20250804jobs.json -> 20250804_jobs.json).

Design principles:
- Root cause first: any missing file or conflicting destination triggers a clear
  error message and non-zero exit code. No silent fallbacks.
- Transparency: verbose, human-readable logs to stdout.
- No external dependencies (stdlib only). Works on Windows.

Usage examples:
- python scripts/weekly_archive.py                # run at app startup; auto no-op unless Monday
- python scripts/weekly_archive.py --normalize    # normalize legacy archive names (idempotent)
- python scripts/weekly_archive.py --dry-run      # show what would happen on Monday

"""
from __future__ import annotations

import argparse
import datetime as dt
import os
import re
import shutil
import sys
from typing import List, Tuple

DATA_DIR = os.path.join("data")
FILES = [
    ("jobs.json", "jobs"),
    ("shortlist.json", "shortlist"),
    ("comprehensive_analysis.json", "comprehensive_analysis"),
]

LEGACY_PATTERN = re.compile(r"^(?P<date>\d{8})(?P<base>jobs|shortlist|comprehensive_analysis)\.json$")
UNDERSCORE_PATTERN = re.compile(r"^(?P<date>\d{8})_(?P<base>jobs|shortlist|comprehensive_analysis)\.json$")


def _log(msg: str) -> None:
    print(f"[weekly_archive] {msg}")


def previous_monday(today: dt.date) -> dt.date:
    """Return the Monday BEFORE 'today'. If today is Monday, return 7 days earlier.
    Example: Mon->previous Monday last week; Tue->yesterday if it was Monday, etc.
    """
    weekday = today.weekday()  # Monday=0 ... Sunday=6
    delta_days = 7 if weekday == 0 else weekday
    return today - dt.timedelta(days=delta_days)


def normalize_legacy_archives(data_dir: str) -> List[Tuple[str, str]]:
    """Rename legacy files like 20250804jobs.json -> 20250804_jobs.json.

    Returns a list of (src, dst) renames performed.
    Fails loudly if a target already exists with different content (we do not compare content).
    """
    performed: List[Tuple[str, str]] = []
    if not os.path.isdir(data_dir):
        raise SystemExit(f"Data directory not found: {data_dir}")

    for name in os.listdir(data_dir):
        m = LEGACY_PATTERN.match(name)
        if not m:
            continue
        date = m.group("date")
        base = m.group("base")
        src = os.path.join(data_dir, name)
        dst_name = f"{date}_{base}.json"
        dst = os.path.join(data_dir, dst_name)
        if os.path.exists(dst):
            # If the normalized target already exists, we skip with an info log (idempotent behavior).
            _log(f"Skip normalization; already exists: {dst_name}")
            continue
        _log(f"Normalize: {name} -> {dst_name}")
        shutil.move(src, dst)
        performed.append((src, dst))
    return performed


def archive_if_monday(data_dir: str, dry_run: bool = False) -> List[Tuple[str, str]]:
    """If today is Monday, archive current files to previous Monday prefix with underscore.

    Idempotent behavior on restarts:
    - If all target archives for the previous Monday already exist, skip and exit 0.
    - If a target exists but the current file also exists (app recreated fresh files), skip that move.
    - If a target and source are both missing for any file, fail loudly (inconsistent state).

    Returns a list of (src, dst) moves that were executed (or would be executed in dry-run).
    """
    today = dt.date.today()
    if today.weekday() != 0:
        _log("Today is not Monday; per requirements, no action taken.")
        return []

    prev = previous_monday(today)
    ymd = prev.strftime("%Y%m%d")
    _log(f"Today is Monday. Target archive date (previous Monday): {ymd}")

    moves: List[Tuple[str, str]] = []
    all_targets_exist = True
    inconsistent: List[str] = []

    for filename, base in FILES:
        src = os.path.join(data_dir, filename)
        dst = os.path.join(data_dir, f"{ymd}_{base}.json")
        src_exists = os.path.exists(src)
        dst_exists = os.path.exists(dst)

        if not dst_exists:
            all_targets_exist = False

        if dst_exists and not src_exists:
            # Already archived earlier; nothing to do
            _log(f"Skip: already archived for {base} -> {os.path.basename(dst)}")
            continue
        if dst_exists and src_exists:
            # Archive exists and current files were recreated; do not overwrite
            _log(f"Skip: destination exists for {base} ({os.path.basename(dst)}); keeping current {filename} intact.")
            continue
        if (not dst_exists) and (not src_exists):
            inconsistent.append(base)
            continue
        if (not dst_exists) and src_exists:
            moves.append((src, dst))

    if inconsistent:
        raise SystemExit(
            "Inconsistent state: missing both source and archive for: " + ", ".join(inconsistent)
        )

    if not moves and all_targets_exist:
        _log(f"All archives for {ymd} already exist; skipping.")
        return []

    for src, dst in moves:
        filename = os.path.basename(src)
        _log(("Would move" if dry_run else "Move") + f": {filename} -> {os.path.basename(dst)}")
        if not dry_run:
            shutil.move(src, dst)

    return moves


def main(argv: List[str]) -> int:
    parser = argparse.ArgumentParser(description="Archive weekly data files (previous Monday prefix) and normalize legacy archives.")
    parser.add_argument("--normalize", action="store_true", help="Normalize legacy archive names (no underscore -> underscore).")
    parser.add_argument("--dry-run", action="store_true", help="Show planned actions without moving files.")
    args = parser.parse_args(argv)

    try:
        if args.normalize:
            _log("Normalizing legacy archive filenames (idempotent)...")
            performed = normalize_legacy_archives(DATA_DIR)
            _log(f"Normalization moves: {len(performed)}")

        moves = archive_if_monday(DATA_DIR, dry_run=args.dry_run)
        _log(f"Archiving moves: {len(moves)}")
        _log("Done.")
        return 0
    except SystemExit as e:
        # If SystemExit is raised with a string, we present it as an error and return code 1.
        msg = str(e)
        if msg:
            _log(f"ERROR: {msg}")
            return 1
        raise
    except Exception as e:
        _log(f"UNEXPECTED ERROR: {e.__class__.__name__}: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))

