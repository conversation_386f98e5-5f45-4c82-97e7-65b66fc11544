#!/usr/bin/env python3
"""
Test the full scraper pipeline for PSP.de to see if jobs are being saved.
"""

import os
import json
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend to path
sys.path.append('backend')

def test_full_psp_scraper():
    """Test the complete scraper pipeline for PSP.de."""
    
    print("=" * 80)
    print("🧪 TESTING FULL PSP.DE SCRAPER PIPELINE")
    print("=" * 80)
    
    try:
        from scraper import JobScraper
        from storage import JobStore

        # Create scraper and storage instances
        scraper = JobScraper()
        storage = JobStore('data/jobs.json')
        
        print("✅ Scraper and storage instances created")
        
        # Test scraping PSP.de specifically
        url = "https://www.psp.de/stellenausschreibungen.html"
        prefix = "AAD"
        
        print(f"🔍 Testing scraper.crawl_url for: {url}")
        
        # Force scrape to bypass cache and see what happens
        jobs = scraper.crawl_url(url, prefix, force_scrape=False)  # Use cache first
        
        print(f"📊 Scraper returned {len(jobs)} jobs")
        
        if jobs:
            print(f"✅ Jobs extracted successfully:")
            for job in jobs[:3]:  # Show first 3
                print(f"   - {job['id']}: {job['title'][:60]}...")
            
            # Test saving to storage
            print(f"\n💾 Testing storage.save_jobs...")
            
            # Check current job count
            current_jobs = storage.data
            current_count = len(current_jobs)
            print(f"   📊 Current jobs in storage: {current_count}")

            # Save the jobs (add them to storage)
            for job in jobs:
                storage.add(job)

            # Check new job count
            new_jobs = storage.data
            new_count = len(new_jobs)
            print(f"   📊 Jobs in storage after save: {new_count}")
            print(f"   📈 Jobs added: {new_count - current_count}")

            # Check if PSP jobs are in storage
            psp_jobs = [job for job in new_jobs if job.get('source') == 'AAD']
            print(f"   🎯 PSP jobs (AAD) in storage: {len(psp_jobs)}")
            
            if psp_jobs:
                print(f"   ✅ PSP jobs found in storage:")
                for job in psp_jobs[:3]:
                    print(f"      - {job['id']}: {job['title'][:50]}...")
            else:
                print(f"   ❌ No PSP jobs found in storage!")
                
        else:
            print(f"❌ No jobs extracted from scraper")
            
            # Debug: check if cache exists
            cache_file = 'data/firecrawl_cache.json'
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    cache_data = json.load(f)
                
                if url in cache_data:
                    print(f"   📁 Cache entry exists for {url}")
                    print(f"   🆔 Crawl ID: {cache_data[url]['crawl_id']}")
                else:
                    print(f"   ❌ No cache entry for {url}")
            else:
                print(f"   ❌ No cache file found")
        
    except Exception as e:
        print(f"❌ Error testing full scraper: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "=" * 80)
    print("🏁 FULL SCRAPER TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_full_psp_scraper()
