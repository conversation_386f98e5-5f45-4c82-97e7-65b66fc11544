#!/usr/bin/env python3
"""
Debug script to simulate the complete PSP.de processing pipeline.
This will help identify where jobs are getting lost in the pipeline.
"""

import os
import json
import logging
from datetime import datetime
from dotenv import load_dotenv
import openai

# Load environment variables
load_dotenv()

# Sample PSP.de pages (simulating what Firecrawl returns)
PSP_PAGES = [
    {
        "markdown": """---
url: "https://www.psp.de/stellenausschreibungen.html"
title: "PSP | Stellenausschreibungen"
---

# Stellenausschreibungen

Hier finden Sie unsere aktuellen Stellenausschreibungen:

- [Bereichsleiter Logistik](https://www.psp.de/stellenausschreibungen.html?stelle=1060)
- [Sales Manager](https://www.psp.de/stellenausschreibungen.html?stelle=1061)
- [IT Director](https://www.psp.de/stellenausschreibungen.html?stelle=1062)
""",
        "metadata": {"sourceURL": "https://www.psp.de/stellenausschreibungen.html"}
    },
    {
        "markdown": """---
url: "https://www.psp.de/stellenausschreibungen.html?stelle=1060"
title: "PSP | Bereichsleiter Logistik & Supply Chain Management"
---

# Stellenausschreibung

## Bereichsleiter Logistik & Supply Chain Management (m/w/d)

Standort: **Hessen**
Branche: **B2B Großhandel**

**Ihre Aufgaben:**
- Führung von 70 Mitarbeitern
- Optimierung logistischer Prozesse
- Entwicklung von Logistikstrategien

**Was Sie mitbringen:**
- Mehrjährige Führungserfahrung
- Logistik-Background
- Hands-on-Mentalität

Kontakt: <EMAIL>
""",
        "metadata": {"sourceURL": "https://www.psp.de/stellenausschreibungen.html?stelle=1060"}
    },
    {
        "markdown": """---
url: "https://www.psp.de/stellenausschreibungen.html?stelle=1061"
title: "PSP | Sales Manager DACH"
---

# Stellenausschreibung

## Sales Manager DACH (m/w/d)

Standort: **München**
Branche: **B2B Software**

**Ihre Aufgaben:**
- Neukundenakquise im DACH-Raum
- Betreuung von Key Accounts
- Vertriebsstrategie entwickeln

**Was Sie mitbringen:**
- 5+ Jahre Vertriebserfahrung
- B2B Software Kenntnisse
- Reisebereitschaft

Kontakt: <EMAIL>
""",
        "metadata": {"sourceURL": "https://www.psp.de/stellenausschreibungen.html?stelle=1061"}
    },
    {
        "markdown": """---
url: "https://www.psp.de/stellenausschreibungen.html?stelle=1062"
title: "PSP | IT Director"
---

# Stellenausschreibung

## IT Director (m/w/d)

Standort: **Berlin**
Branche: **Fintech**

**Ihre Aufgaben:**
- Leitung der IT-Abteilung
- Digitalisierungsstrategie
- Team von 25 Entwicklern führen

**Was Sie mitbringen:**
- 10+ Jahre IT-Führungserfahrung
- Cloud-Expertise
- Agile Methodiken

Kontakt: <EMAIL>
""",
        "metadata": {"sourceURL": "https://www.psp.de/stellenausschreibungen.html?stelle=1062"}
    }
]

def simulate_scraper_pipeline():
    """Simulate the complete scraper pipeline for PSP.de."""
    
    print("=" * 80)
    print("🔧 SIMULATING COMPLETE PSP.DE SCRAPER PIPELINE")
    print("=" * 80)
    
    # Check OpenAI API key
    openai_api_key = os.getenv('OPENAI_API_KEY')
    if not openai_api_key:
        print("❌ OPENAI_API_KEY not found in environment")
        return
    
    client = openai.OpenAI(api_key=openai_api_key)
    
    # Simulate the scraper processing
    source_prefix = "AAD"  # PSP.de prefix
    url = "https://www.psp.de/stellenausschreibungen.html"
    
    print(f"📡 Simulating crawl of: {url}")
    print(f"🔍 Processing {len(PSP_PAGES)} pages...")
    print()
    
    all_jobs = []
    
    # Process each page (simulating the scraper loop)
    for i, page in enumerate(PSP_PAGES):
        page_markdown = page.get('markdown')
        page_url = page.get('metadata', {}).get('sourceURL', url)
        
        print(f"📄 Processing page {i+1}/{len(PSP_PAGES)}: {page_url}")
        
        if not page_markdown:
            print(f"   ⚠️  No markdown content, skipping")
            continue
            
        print(f"   📝 Content length: {len(page_markdown)} characters")
        
        # Extract jobs using LLM (same logic as scraper)
        jobs = extract_jobs_with_llm(client, page_markdown, page_url, source_prefix, i+1)
        
        if jobs:
            all_jobs.extend(jobs)
            print(f"   ✅ Found {len(jobs)} jobs on this page")
            for job in jobs:
                print(f"      - {job['id']}: {job['title']}")
        else:
            print(f"   ❌ No jobs found on this page")
        print()
    
    print(f"🎯 TOTAL EXTRACTED: {len(all_jobs)} jobs from {url}")
    print()
    
    # Simulate saving to storage
    print(f"💾 SIMULATING STORAGE SAVE:")
    
    if all_jobs:
        # Save the jobs (simulating storage.py logic)
        timestamp = datetime.now().isoformat()
        
        for job in all_jobs:
            job['created_at'] = timestamp
            job['updated_at'] = timestamp
        
        # Save to debug file
        with open('debug_psp_pipeline_jobs.json', 'w', encoding='utf-8') as f:
            json.dump(all_jobs, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ Would save {len(all_jobs)} jobs to database")
        print(f"   💾 Debug jobs saved to: debug_psp_pipeline_jobs.json")
        
        # Show what would be saved
        for job in all_jobs:
            print(f"      {job['id']}: {job['title'][:50]}...")
    else:
        print(f"   ❌ No jobs to save!")
    
    print(f"\n" + "=" * 80)
    print("🏁 PIPELINE SIMULATION COMPLETE")
    print("=" * 80)

def extract_jobs_with_llm(client, content, source_url, source_prefix, page_number):
    """Extract jobs using LLM (same logic as scraper._extract_jobs_with_llm)."""
    
    # Use the exact same prompt as the scraper
    prompt = f"""
    Extract ONLY actual job postings from this content. Focus on complete job descriptions with detailed information.

    IMPORTANT RULES:
    1. Only extract jobs that have DETAILED descriptions, requirements, or responsibilities
    2. Ignore navigation menus, breadcrumbs, image alt text, and lists of job titles without details
    3. Ignore content that appears to be just job title lists or category navigation
    4. Each job must have substantial content beyond just a title and location
    5. If you find multiple jobs on the same page, each must have its own unique detail URL
    6. If jobs share the same URL, only extract the MAIN job posting from that page

    Look for these indicators of a REAL job posting:
    - Detailed job description or responsibilities section
    - Requirements or qualifications section
    - Salary information or benefits
    - Application instructions or contact information
    - Company description related to the specific role

    For each ACTUAL job posting found, return:
    {{
        "title": "exact job title from the main job posting",
        "company": "company name if found, otherwise 'Not specified'",
        "location": "location if found, otherwise 'Not specified'",
        "summary": "brief summary from actual job description (max 150 chars)",
        "detail_url": "direct job URL if found, otherwise '{source_url}'"
    }}

    Return ONLY a valid JSON array. If you only find one main job posting, return an array with one job.
    Do NOT extract jobs from navigation elements, image alt text, or title lists.

    Content:
    {content[:8000]}
    """
    
    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a job listing extraction expert. You MUST return valid JSON array format. Extract ALL job postings from the content, even if information is incomplete."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=4000,
            temperature=0.0
        )
        
        response_text = response.choices[0].message.content.strip()
        
        # Parse JSON response (same logic as scraper)
        import re
        
        try:
            jobs_data = json.loads(response_text)
        except json.JSONDecodeError:
            try:
                json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
                if json_match:
                    jobs_data = json.loads(json_match.group())
                else:
                    print(f"   ❌ No JSON found in LLM response")
                    return []
            except json.JSONDecodeError:
                print(f"   ❌ JSON parsing failed")
                return []
        
        # Process jobs (same logic as scraper)
        processed_jobs = []
        
        for i, job_data in enumerate(jobs_data[:20]):  # Limit to 20 jobs per page
            job_id = f"{source_prefix}{page_number:02d}{i+1:02d}"  # e.g., AAD0101, AAD0201
            
            job_url = job_data.get("detail_url", job_data.get("link", source_url))
            
            job = {
                "id": job_id,
                "title": job_data.get("title", "Unknown Position"),
                "company": job_data.get("company", "Not specified"),
                "location": job_data.get("location", "Not specified"),
                "description": job_data.get("summary", "Not specified"),
                "source": source_prefix,
                "link": job_url,
                "isShortlisted": False,
                "scraped_at": datetime.now().isoformat()
            }
            processed_jobs.append(job)
        
        return processed_jobs
        
    except Exception as e:
        print(f"   ❌ LLM extraction error: {e}")
        return []

if __name__ == "__main__":
    simulate_scraper_pipeline()
