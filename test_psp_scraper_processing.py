#!/usr/bin/env python3
"""
Test script to simulate exactly what the scraper does when processing
the cached PSP.de Firecrawl data to see where jobs are getting lost.
"""

import os
import json
import logging
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_scraper_processing():
    """Test the exact scraper processing logic with real PSP.de cached data."""
    
    print("=" * 80)
    print("🧪 TESTING SCRAPER PROCESSING WITH REAL PSP.DE DATA")
    print("=" * 80)
    
    # Load the actual cached Firecrawl data
    debug_file = 'debug_firecrawl_pages_20250804_164442.json'
    if not os.path.exists(debug_file):
        print(f"❌ Debug file {debug_file} not found")
        return
    
    with open(debug_file, 'r', encoding='utf-8') as f:
        crawl_data = json.load(f)
    
    pages_data = crawl_data.get('pages', [])
    print(f"📄 Loaded {len(pages_data)} pages from cached crawl data")
    
    # Simulate the exact scraper processing logic
    url = "https://www.psp.de/stellenausschreibungen.html"
    source_prefix = "AAD"
    
    # Import the scraper to use its actual methods
    try:
        import sys
        sys.path.append('backend')
        from scraper import JobScraper
        
        # Create scraper instance
        scraper = JobScraper()
        
        print(f"✅ Scraper instance created successfully")
        
        # Process all pages exactly like the scraper does
        all_jobs = []
        for i, page in enumerate(pages_data):
            print(f"\n📄 Processing page {i+1}/{len(pages_data)}:")
            
            # Handle different page data structures from Firecrawl SDK (exact scraper logic)
            page_markdown = None
            page_url = url
            
            # Current Firecrawl SDK returns dict with 'markdown' and 'metadata' keys
            if isinstance(page, dict):
                page_markdown = page.get('markdown')
                if page.get('metadata') and isinstance(page['metadata'], dict):
                    page_url = page['metadata'].get('sourceURL', url)
                print(f"   📍 URL: {page_url}")
                print(f"   📝 Markdown length: {len(page_markdown) if page_markdown else 0}")
            # Fallback for object-based responses
            elif hasattr(page, 'markdown'):
                page_markdown = page.markdown
                if hasattr(page, 'metadata'):
                    if hasattr(page.metadata, 'sourceURL'):
                        page_url = page.metadata.sourceURL
                    elif isinstance(page.metadata, dict):
                        page_url = page.metadata.get('sourceURL', url)
                print(f"   📍 URL: {page_url}")
                print(f"   📝 Markdown length: {len(page_markdown) if page_markdown else 0}")
            
            if page_markdown:
                print(f"   🔍 Calling _extract_jobs_with_llm...")
                
                # Call the actual scraper method
                jobs = scraper._extract_jobs_with_llm(
                    page_markdown,
                    page_url,
                    source_prefix,
                    page_number=i+1,
                    log_callback=None
                )
                
                if jobs:
                    all_jobs.extend(jobs)
                    print(f"   ✅ Found {len(jobs)} jobs on this page")
                    for job in jobs:
                        print(f"      - {job['id']}: {job['title'][:60]}...")
                else:
                    print(f"   ❌ No jobs found on this page")
                    
                    # Debug: show content preview
                    preview = page_markdown[:500].replace('\n', '\\n')
                    print(f"   📄 Content preview: {preview}...")
            else:
                print(f"   ⚠️  Page {i+1} has no markdown content, skipping")
                # Debug: show what we actually got
                if isinstance(page, dict):
                    print(f"      Available keys: {list(page.keys())}")
                else:
                    print(f"      Page type: {type(page)}, attributes: {[attr for attr in dir(page) if not attr.startswith('_')]}")
        
        print(f"\n🎯 TOTAL EXTRACTED: {len(all_jobs)} jobs from PSP.de cached data")
        
        if all_jobs:
            # Save the extracted jobs for inspection
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"test_psp_extracted_jobs_{timestamp}.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_jobs, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Extracted jobs saved to: {output_file}")
            
            # Show summary
            print(f"\n📊 JOB EXTRACTION SUMMARY:")
            for job in all_jobs:
                print(f"   {job['id']}: {job['title']}")
                print(f"      Company: {job['company']}")
                print(f"      Location: {job['location']}")
                print(f"      Link: {job['link']}")
                print()
        else:
            print(f"❌ NO JOBS EXTRACTED - This is the problem!")
            
    except Exception as e:
        print(f"❌ Error testing scraper processing: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "=" * 80)
    print("🏁 SCRAPER PROCESSING TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_scraper_processing()
