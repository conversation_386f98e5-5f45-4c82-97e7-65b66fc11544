#!/usr/bin/env python3
"""
Check which cached crawl IDs have expired on Firecrawl servers.
"""

import os
import json
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the backend directory to the Python path
sys.path.append('backend')

def check_cache_expiry():
    """Check which cached crawl IDs are still valid on Firecrawl."""
    
    print("🔍 CHECKING FIRECRAWL CACHE EXPIRY")
    print("=" * 50)
    
    # Check if Firecrawl API key is available
    firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
    if not firecrawl_api_key:
        print("❌ FIRECRAWL_API_KEY not found in environment")
        return
    
    # Load cache data
    cache_file = 'data/firecrawl_cache.json'
    if not os.path.exists(cache_file):
        print(f"❌ Cache file {cache_file} not found")
        return
    
    with open(cache_file, 'r', encoding='utf-8') as f:
        cache_data = json.load(f)
    
    print(f"✅ Cache loaded with {len(cache_data)} entries")
    
    # Initialize Firecrawl
    try:
        from firecrawl import FirecrawlApp
        firecrawl = FirecrawlApp(api_key=firecrawl_api_key)
        print("✅ Firecrawl client initialized")
    except Exception as e:
        print(f"❌ Error initializing Firecrawl: {e}")
        return
    
    # Check timestamps and validity
    now = datetime.now()
    valid_count = 0
    expired_count = 0
    error_count = 0
    
    # Focus on the first few URLs that are problematic
    test_urls = [
        "https://www.hapeko.de/bewerbende/stellenangebote/p1",
        "https://www.psp.de/stellenausschreibungen.html",
        "https://www.mentis-consulting.de/aktueller-stellenmarkt/",
        "https://capera.starhunter.software/job-portal/overview"
    ]
    
    for url in test_urls:
        if url not in cache_data:
            print(f"❌ {url} not in cache")
            continue
            
        crawl_info = cache_data[url]
        crawl_id = crawl_info.get('crawl_id')
        timestamp_str = crawl_info.get('timestamp')
        
        # Parse timestamp
        try:
            timestamp = datetime.fromisoformat(timestamp_str)
            age_hours = (now - timestamp).total_seconds() / 3600
        except:
            timestamp = None
            age_hours = 0
        
        print(f"\n🔍 {url}")
        print(f"   🆔 Crawl ID: {crawl_id[:8]}...")
        print(f"   📅 Cached: {timestamp_str}")
        print(f"   ⏰ Age: {age_hours:.1f} hours")
        
        # Check if still valid on Firecrawl
        try:
            status = firecrawl.check_crawl_status(crawl_id)
            if status:
                print(f"   ✅ Status: {status.status}")
                if hasattr(status, 'data') and status.data:
                    print(f"   📄 Pages: {len(status.data)}")
                valid_count += 1
            else:
                print(f"   ❌ No status returned")
                expired_count += 1
        except Exception as e:
            if "404" in str(e) or "expired" in str(e).lower():
                print(f"   ❌ EXPIRED: {e}")
                expired_count += 1
            else:
                print(f"   ⚠️ ERROR: {e}")
                error_count += 1
    
    print(f"\n📊 SUMMARY:")
    print(f"   ✅ Valid: {valid_count}")
    print(f"   ❌ Expired: {expired_count}")
    print(f"   ⚠️ Errors: {error_count}")
    
    if expired_count > 0:
        print(f"\n💡 SOLUTION:")
        print(f"   The cached crawl IDs have expired on Firecrawl servers.")
        print(f"   The 'Reload from Firecrawl' button cannot recover expired crawls.")
        print(f"   You need to run a fresh scrape to get new crawl IDs.")
        print(f"   Consider reducing FIRECRAWL_CACHE_HOURS or running scrapes more frequently.")

if __name__ == "__main__":
    check_cache_expiry()
