#!/usr/bin/env python3
"""
Test a single URL that's scraped but missing from job listings.
"""

import os
import json
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend to path
sys.path.append('backend')

def test_single_url():
    """Test bollmann-executives URL specifically."""
    
    print("=" * 80)
    print("🧪 TESTING: bollmann-executives.de")
    print("=" * 80)
    
    url = "https://www.bollmann-executives.de/ihre-karriere/stellenangebote"
    prefix = "AAW"
    
    try:
        from scraper import JobScraper
        
        # Create scraper instance
        scraper = JobScraper()
        print("✅ Scraper instance created")
        
        # Check if URL is in Firecrawl cache
        cache_file = 'data/firecrawl_cache.json'
        if os.path.exists(cache_file):
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)
            
            if url in cache_data:
                crawl_info = cache_data[url]
                print(f"✅ Found in Firecrawl cache:")
                print(f"   🆔 Crawl ID: {crawl_info['crawl_id']}")
                print(f"   📅 Timestamp: {crawl_info['timestamp']}")
                
                # Test scraping this URL
                print(f"🔍 Testing scraper.crawl_url...")
                jobs = scraper.crawl_url(url, prefix, force_scrape=False)
                
                print(f"📊 Scraper returned: {len(jobs)} jobs")
                
                if jobs:
                    print(f"✅ Jobs extracted successfully:")
                    for job in jobs:
                        print(f"   - {job['id']}: {job['title']}")
                        print(f"     Company: {job['company']}")
                        print(f"     Location: {job['location']}")
                        print(f"     Link: {job['link']}")
                        print()
                else:
                    print(f"❌ No jobs extracted - investigating...")
                    
                    # Check the cached crawl data directly
                    try:
                        from firecrawl import FirecrawlApp
                        firecrawl = FirecrawlApp(api_key=os.getenv('FIRECRAWL_API_KEY'))
                        
                        crawl_status = firecrawl.check_crawl_status(crawl_info['crawl_id'])
                        if crawl_status:
                            print(f"   📊 Crawl status: {crawl_status.status}")
                            if crawl_status.data:
                                print(f"   📄 Pages in crawl: {len(crawl_status.data)}")
                                
                                # Show first page content
                                first_page = crawl_status.data[0]
                                if isinstance(first_page, dict):
                                    markdown = first_page.get('markdown', '')
                                    print(f"   📝 First page markdown length: {len(markdown)}")
                                    if markdown:
                                        preview = markdown[:500].replace('\n', '\\n')
                                        print(f"   📄 Content preview: {preview}...")
                                        
                                        # Check for job-related keywords
                                        job_keywords = ['job', 'position', 'stelle', 'karriere', 'bewerbung', 'vacancy', 'career']
                                        found_keywords = [kw for kw in job_keywords if kw.lower() in markdown.lower()]
                                        print(f"   🔍 Job keywords found: {found_keywords}")
                                    else:
                                        print(f"   ❌ No markdown content in first page")
                                else:
                                    print(f"   ❓ First page type: {type(first_page)}")
                            else:
                                print(f"   ❌ No data in crawl")
                        else:
                            print(f"   ❌ Could not get crawl status")
                            
                    except Exception as e:
                        print(f"   ❌ Error checking crawl: {e}")
            else:
                print(f"❌ URL not found in Firecrawl cache")
        else:
            print(f"❌ No Firecrawl cache file found")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "=" * 80)
    print("🏁 TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_single_url()
