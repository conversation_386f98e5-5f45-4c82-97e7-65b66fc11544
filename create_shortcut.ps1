# Create Desktop and Start Menu shortcuts for Jomade MVP
# Run this script once to create shortcuts that you can pin to taskbar

Write-Host "Creating Jomade MVP shortcuts..." -ForegroundColor Green

# Get current directory
$AppDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$BatchFile = Join-Path $AppDir "start_jomade.bat"
$PowerShellFile = Join-Path $AppDir "start_jomade.ps1"

# Create WScript Shell object
$WshShell = New-Object -comObject WScript.Shell

# Desktop shortcut
$DesktopPath = [System.Environment]::GetFolderPath('Desktop')
$DesktopShortcut = Join-Path $DesktopPath "Jomade MVP.lnk"

Write-Host "📁 Creating desktop shortcut: $DesktopShortcut" -ForegroundColor Blue

$Shortcut = $WshShell.CreateShortcut($DesktopShortcut)
$Shortcut.TargetPath = $BatchFile
$Shortcut.WorkingDirectory = $AppDir
$Shortcut.Description = "Jomade MVP - Executive Search Application"
$Shortcut.IconLocation = "shell32.dll,21"  # Globe icon
$Shortcut.Save()

# Start Menu shortcut
$StartMenuPath = [System.Environment]::GetFolderPath('StartMenu')
$ProgramsPath = Join-Path $StartMenuPath "Programs"
$StartMenuShortcut = Join-Path $ProgramsPath "Jomade MVP.lnk"

Write-Host "📁 Creating start menu shortcut: $StartMenuShortcut" -ForegroundColor Blue

$Shortcut = $WshShell.CreateShortcut($StartMenuShortcut)
$Shortcut.TargetPath = $BatchFile
$Shortcut.WorkingDirectory = $AppDir
$Shortcut.Description = "Jomade MVP - Executive Search Application"
$Shortcut.IconLocation = "shell32.dll,21"  # Globe icon
$Shortcut.Save()

# PowerShell version shortcut (alternative)
$PowerShellShortcut = Join-Path $DesktopPath "Jomade MVP (PowerShell).lnk"

Write-Host "📁 Creating PowerShell shortcut: $PowerShellShortcut" -ForegroundColor Blue

$Shortcut = $WshShell.CreateShortcut($PowerShellShortcut)
$Shortcut.TargetPath = "powershell.exe"
$Shortcut.Arguments = "-ExecutionPolicy Bypass -File `"$PowerShellFile`" -OpenBrowser"
$Shortcut.WorkingDirectory = $AppDir
$Shortcut.Description = "Jomade MVP - Executive Search Application (PowerShell)"
$Shortcut.IconLocation = "shell32.dll,21"  # Globe icon
$Shortcut.Save()

Write-Host ""
Write-Host "✅ Shortcuts created successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📌 To pin to taskbar:" -ForegroundColor Cyan
Write-Host "   1. Right-click on the desktop shortcut 'Jomade MVP'" -ForegroundColor White
Write-Host "   2. Select 'Pin to taskbar'" -ForegroundColor White
Write-Host ""
Write-Host "🚀 You can now start Jomade MVP from:" -ForegroundColor Yellow
Write-Host "   • Desktop shortcut" -ForegroundColor White
Write-Host "   • Start Menu" -ForegroundColor White
Write-Host "   • Taskbar (after pinning)" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
