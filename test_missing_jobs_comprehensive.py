#!/usr/bin/env python3
"""
Comprehensive test to identify why some URLs that are successfully scraped by Firecrawl
are not showing up in the job listings.
"""

import os
import json
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend to path
sys.path.append('backend')

def test_missing_jobs():
    """Test URLs that are scraped but missing from job listings."""
    
    print("=" * 80)
    print("🔍 COMPREHENSIVE TEST: MISSING JOBS FROM SCRAPED URLS")
    print("=" * 80)
    
    # URLs to test (known to be scraped but missing jobs)
    test_urls = [
        {
            "url": "https://www.bollmann-executives.de/ihre-karriere/stellenangebote",
            "prefix": "AAW",
            "name": "bollmann-executives"
        },
        {
            "url": "https://delphi-hr.de/jobs/",
            "prefix": "AAZ", 
            "name": "delphi-hr"
        },
        {
            "url": "https://www.qrc-group.com/jobs/",
            "prefix": "ABR",
            "name": "qrc-group"
        }
    ]
    
    try:
        from scraper import JobScraper
        from storage import JobStore
        
        # Create scraper and storage instances
        scraper = JobScraper()
        storage = JobStore('data/jobs.json')
        
        print("✅ Scraper and storage instances created")
        
        # Check current job counts by source
        current_jobs = storage.data
        print(f"📊 Total jobs in database: {len(current_jobs)}")
        
        # Test each URL
        for test_url in test_urls:
            url = test_url["url"]
            prefix = test_url["prefix"]
            name = test_url["name"]
            
            print(f"\n" + "="*60)
            print(f"🧪 TESTING: {name} ({prefix})")
            print(f"📍 URL: {url}")
            print(f"="*60)
            
            # Check if jobs exist in database for this source
            existing_jobs = [job for job in current_jobs if job.get('source') == prefix]
            print(f"📊 Existing jobs in database: {len(existing_jobs)}")
            
            # Check if URL is in Firecrawl cache
            cache_file = 'data/firecrawl_cache.json'
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    cache_data = json.load(f)
                
                if url in cache_data:
                    crawl_info = cache_data[url]
                    print(f"✅ Found in Firecrawl cache:")
                    print(f"   🆔 Crawl ID: {crawl_info['crawl_id']}")
                    print(f"   📅 Timestamp: {crawl_info['timestamp']}")
                    
                    # Test scraping this URL
                    print(f"🔍 Testing scraper.crawl_url...")
                    jobs = scraper.crawl_url(url, prefix, force_scrape=False)
                    
                    print(f"📊 Scraper returned: {len(jobs)} jobs")
                    
                    if jobs:
                        print(f"✅ Jobs extracted successfully:")
                        for i, job in enumerate(jobs[:3]):  # Show first 3
                            print(f"   {i+1}. {job['id']}: {job['title'][:50]}...")
                            print(f"      Company: {job['company']}")
                            print(f"      Location: {job['location']}")
                        
                        if len(jobs) > 3:
                            print(f"   ... and {len(jobs) - 3} more jobs")
                        
                        # Test saving to storage
                        print(f"💾 Testing storage save...")
                        jobs_before = len(storage.data)
                        
                        for job in jobs:
                            storage.add(job)
                        
                        jobs_after = len(storage.data)
                        jobs_added = jobs_after - jobs_before
                        
                        print(f"   📈 Jobs added to storage: {jobs_added}")
                        
                        # Verify jobs are now in storage
                        new_jobs = [job for job in storage.data if job.get('source') == prefix]
                        print(f"   🎯 Total {prefix} jobs now in storage: {len(new_jobs)}")
                        
                    else:
                        print(f"❌ No jobs extracted")
                        
                        # Debug: Check what's in the cached crawl
                        print(f"🔍 Debugging cached crawl...")
                        try:
                            from firecrawl import FirecrawlApp
                            firecrawl = FirecrawlApp(api_key=os.getenv('FIRECRAWL_API_KEY'))
                            
                            crawl_status = firecrawl.check_crawl_status(crawl_info['crawl_id'])
                            if crawl_status and crawl_status.data:
                                print(f"   📄 Cached crawl has {len(crawl_status.data)} pages")
                                
                                # Check first page content
                                if len(crawl_status.data) > 0:
                                    first_page = crawl_status.data[0]
                                    if isinstance(first_page, dict):
                                        markdown = first_page.get('markdown', '')
                                        print(f"   📝 First page content length: {len(markdown)}")
                                        if markdown:
                                            preview = markdown[:200].replace('\n', '\\n')
                                            print(f"   📄 Content preview: {preview}...")
                                        else:
                                            print(f"   ❌ First page has no markdown content")
                                    else:
                                        print(f"   ❓ First page type: {type(first_page)}")
                            else:
                                print(f"   ❌ Could not retrieve cached crawl data")
                                
                        except Exception as e:
                            print(f"   ❌ Error checking cached crawl: {e}")
                else:
                    print(f"❌ Not found in Firecrawl cache")
            else:
                print(f"❌ No Firecrawl cache file found")
        
        # Final summary
        print(f"\n" + "="*80)
        print(f"📊 FINAL SUMMARY")
        print(f"="*80)
        
        final_jobs = storage.data
        print(f"📊 Total jobs in database: {len(final_jobs)}")
        
        for test_url in test_urls:
            prefix = test_url["prefix"]
            name = test_url["name"]
            jobs_for_source = [job for job in final_jobs if job.get('source') == prefix]
            print(f"   {name} ({prefix}): {len(jobs_for_source)} jobs")
        
    except Exception as e:
        print(f"❌ Error in comprehensive test: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "=" * 80)
    print("🏁 COMPREHENSIVE TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_missing_jobs()
