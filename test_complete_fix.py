#!/usr/bin/env python3
"""
Test the complete fix: timeout + JSON parsing for delphi-hr.de
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend to path
sys.path.append('backend')

def test_complete_fix():
    """Test the complete fix for delphi-hr.de."""
    
    print("=" * 80)
    print("🧪 TESTING COMPLETE FIX: TIMEOUT + JSON PARSING")
    print("=" * 80)
    
    try:
        from scraper import JobScraper
        from storage import JobStore
        
        # Create instances
        scraper = JobScraper()
        storage = JobStore('data/jobs.json')
        
        url = "https://delphi-hr.de/jobs/"
        prefix = "AAZ"
        
        print(f"📍 URL: {url}")
        print(f"🔧 Timeout: {scraper.timeout_seconds}s (15 minutes)")
        print(f"📊 Current AAZ jobs in database: {len([job for job in storage.data if job.get('source') == 'AAZ'])}")
        
        # Force a new crawl to test the fixes
        print(f"🚀 Testing with force_scrape=True to test fixes...")
        jobs = scraper.crawl_url(url, prefix, force_scrape=True)
        
        print(f"📊 Result: {len(jobs)} jobs extracted")
        
        if jobs:
            print(f"✅ SUCCESS: Complete fix worked! Jobs extracted:")
            for job in jobs:
                print(f"   - {job['id']}: {job['title']}")
                print(f"     Company: {job['company']}")
                print(f"     Location: {job['location']}")
                print()
            
            # Save to database
            print(f"💾 Saving jobs to database...")
            for job in jobs:
                storage.add(job)
            
            final_aaz_jobs = [job for job in storage.data if job.get('source') == 'AAZ']
            print(f"📊 Final AAZ jobs in database: {len(final_aaz_jobs)}")
            
        else:
            print(f"❌ No jobs extracted - check logs above for issues")
        
    except Exception as e:
        print(f"❌ Error testing complete fix: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "=" * 80)
    print("🏁 COMPLETE FIX TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_complete_fix()
