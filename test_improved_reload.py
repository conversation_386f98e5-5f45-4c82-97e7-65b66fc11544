#!/usr/bin/env python3
"""
Test the improved "Reload from Firecrawl" functionality with fallback crawls.
"""

import os
import json
import sys
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the backend directory to the Python path
sys.path.append('backend')

def test_improved_reload():
    """Test the improved reload functionality."""
    
    print("🔍 TESTING IMPROVED RELOAD FROM FIRECRAWL")
    print("=" * 60)
    
    # Check if Firecrawl API key is available
    firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
    if not firecrawl_api_key:
        print("❌ FIRECRAWL_API_KEY not found in environment")
        return
    
    print(f"✅ Firecrawl API key found: {firecrawl_api_key[:8]}...")
    
    # Import scraper
    try:
        from scraper import create_scraper
        scraper = create_scraper(timeout_seconds=300, cache_hours=24)
        if not scraper:
            print("❌ Failed to create scraper")
            return
        print("✅ Scraper created successfully")
    except Exception as e:
        print(f"❌ Error creating scraper: {e}")
        return
    
    # Test URLs that are showing 0 jobs but have expired Firecrawl cache
    test_urls = [
        {
            "url": "https://www.hapeko.de/bewerbende/stellenangebote/p1",
            "prefix": "AAA"
        },
        {
            "url": "https://www.psp.de/stellenausschreibungen.html", 
            "prefix": "AAD"
        }
    ]
    
    print(f"\n🔄 Testing download_cached_multiple with fallback crawls...")
    
    try:
        result = scraper.download_cached_multiple(test_urls, fetch_details=False)
        
        print(f"\n📊 RESULTS:")
        print(f"   ✅ Success: {result.get('success', False)}")
        print(f"   📄 Total jobs: {result.get('job_count', 0)}")
        print(f"   🎯 Successful prefixes: {result.get('successful_prefixes', [])}")
        print(f"   🔄 Crawled prefixes: {result.get('crawled_prefixes', [])}")
        print(f"   ⏰ Expired crawls: {result.get('expired_crawls', [])}")
        print(f"   ❌ Failed URLs: {result.get('failed_urls', [])}")
        print(f"   💬 Message: {result.get('message', 'N/A')}")
        
        if result.get('jobs'):
            print(f"\n📋 JOBS FOUND:")
            for job in result['jobs'][:5]:  # Show first 5 jobs
                print(f"   • {job.get('id', 'N/A')}: {job.get('title', 'N/A')}")
                print(f"     Company: {job.get('company', 'N/A')}")
                print(f"     Location: {job.get('location', 'N/A')}")
        else:
            print(f"\n❌ No jobs found")
            
    except Exception as e:
        print(f"❌ Error testing improved reload: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_improved_reload()
