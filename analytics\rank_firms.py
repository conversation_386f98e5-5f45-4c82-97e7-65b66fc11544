"""
Firm ranking analytics (simple, explicit, fail-loudly) for the last N weeks.

- Reads archived weekly data from data/ where files are named either
  YYYYMMDD_jobs.json / YYYYMMDD_shortlist.json / YYYYMMDD_comprehensive_analysis.json
  or their legacy variants without underscore (we normalize, but still read both).
- Computes simple per-firm metrics with a recency window of 10 weeks by default.
- Outputs a single consolidated JSON file at data/firm_ranking.json for the
  settings page to consume. No CSV, no charts here.

Scoring model (intentionally simple):
- For each firm (source):
  - scraped_count per week from jobs archives
  - shortlisted_count per week from shortlist archives
  - avg_match_score per week from shortlist (top-level match_score)
  - yield_rate = shortlisted_count / scraped_count (per week; 0 if no scraped)
- Recency window: last N weeks ordered by date; no exponential decay to keep it simple.
- Empirical-Bayes shrinkage: shrink average match_score toward global mean with
  prior_weight=10 to avoid overrating tiny samples.
- Trend: sign over the window using simple least squares slope of weekly avg_match_score.
- Composite score (0–100): 0.6 * quality + 0.3 * yield + 0.1 * trend_component
  where quality = 100 * shrunk_avg_match_score,
        yield = 100 * (total_shortlisted / total_scraped),
        trend_component = normalized slope mapped into 0..100 (clipped).

Design principles:
- No hidden fallbacks: missing weeks are logged. If no archives present, exit non-zero.
- Clear, human-readable console logs; single JSON output only.
- No external dependencies.
"""
from __future__ import annotations

import argparse
import json
import os
import re
import statistics
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

DATA_DIR = os.path.join("data")
ARCHIVE_BASENAMES = ("jobs", "shortlist")  # we don't need comprehensive_analysis for this minimal model
LEGACY_PATTERN = re.compile(r"^(?P<date>\d{8})(?P<base>jobs|shortlist|comprehensive_analysis)\.json$")
UNDERSCORE_PATTERN = re.compile(r"^(?P<date>\d{8})_(?P<base>jobs|shortlist|comprehensive_analysis)\.json$")


@dataclass
class WeeklyFirmStats:
    date: str  # YYYYMMDD
    scraped: int = 0
    shortlisted: int = 0
    avg_match_score: Optional[float] = None  # 0..1 or None if no shortlist


def _log(msg: str) -> None:
    print(f"[rank_firms] {msg}")


def _list_archives() -> Dict[str, Dict[str, str]]:
    """Return mapping date -> { base: path } for archives we can find.

    Accepts both underscore and legacy filename styles.
    """
    if not os.path.isdir(DATA_DIR):
        raise SystemExit(f"Data directory not found: {DATA_DIR}")

    by_date: Dict[str, Dict[str, str]] = {}
    for name in os.listdir(DATA_DIR):
        for pat in (UNDERSCORE_PATTERN, LEGACY_PATTERN):
            m = pat.match(name)
            if m:
                date = m.group("date")
                base = m.group("base")
                by_date.setdefault(date, {})[base] = os.path.join(DATA_DIR, name)
                break
    return by_date


def _load_json_array(path: str) -> List[dict]:
    with open(path, "r", encoding="utf-8") as f:
        data = json.load(f)
        if not isinstance(data, list):
            raise SystemExit(f"Expected array in {path}")
        return data


def _gather_weekly(by_date: Dict[str, Dict[str, str]]) -> Dict[str, Dict[str, WeeklyFirmStats]]:
    """Return mapping date -> firm -> WeeklyFirmStats"""
    weekly: Dict[str, Dict[str, WeeklyFirmStats]] = {}

    for date, files in by_date.items():
        by_firm: Dict[str, WeeklyFirmStats] = {}
        # Scraped counts from jobs
        jobs_path = files.get("jobs")
        if jobs_path:
            for job in _load_json_array(jobs_path):
                firm = job.get("source")
                if not firm:
                    continue
                s = by_firm.setdefault(firm, WeeklyFirmStats(date=date))
                s.scraped += 1
        # Shortlist metrics
        shortlist_path = files.get("shortlist")
        if shortlist_path:
            scores_by_firm: Dict[str, List[float]] = {}
            counts_by_firm: Dict[str, int] = {}
            for item in _load_json_array(shortlist_path):
                job_data = item.get("job_data", {})
                firm = job_data.get("source")
                if not firm:
                    continue
                match_score = item.get("match_score")
                if isinstance(match_score, (int, float)):
                    scores_by_firm.setdefault(firm, []).append(float(match_score))
                counts_by_firm[firm] = counts_by_firm.get(firm, 0) + 1
            for firm, cnt in counts_by_firm.items():
                s = by_firm.setdefault(firm, WeeklyFirmStats(date=date))
                s.shortlisted += cnt
                scores = scores_by_firm.get(firm)
                if scores:
                    s.avg_match_score = sum(scores) / len(scores)
        weekly[date] = by_firm

    return weekly


def _last_n_weeks(sorted_dates: List[str], n: int) -> List[str]:
    return sorted_dates[-n:]


def _slope(xs: List[int], ys: List[float]) -> float:
    """Return least-squares slope. If insufficient points or zero variance, return 0."""
    if len(xs) < 2:
        return 0.0
    mean_x = statistics.fmean(xs)
    mean_y = statistics.fmean(ys)
    denom = sum((x - mean_x) ** 2 for x in xs)
    if denom == 0:
        return 0.0
    num = sum((x - mean_x) * (y - mean_y) for x, y in zip(xs, ys))
    return num / denom


def compute_ranking(window_weeks: int = 10, prior_weight: float = 10.0) -> dict:
    by_date = _list_archives()
    if not by_date:
        raise SystemExit("No archives found in data/; cannot compute rankings.")

    weekly = _gather_weekly(by_date)
    dates = sorted(weekly.keys())
    window_dates = _last_n_weeks(dates, window_weeks)
    if not window_dates:
        raise SystemExit("No dates available after applying window.")

    # Global mean for EB shrinkage
    all_scores: List[float] = []
    for d in window_dates:
        for s in weekly[d].values():
            if s.avg_match_score is not None:
                all_scores.append(s.avg_match_score)
    global_mean = statistics.fmean(all_scores) if all_scores else 0.5

    # Aggregate per firm over window
    per_firm: Dict[str, dict] = {}
    for d in window_dates:
        for firm, s in weekly[d].items():
            agg = per_firm.setdefault(
                firm,
                {
                    "scraped": 0,
                    "shortlisted": 0,
                    "match_scores": [],
                    "weekly": [],
                },
            )
            agg["scraped"] += s.scraped
            agg["shortlisted"] += s.shortlisted
            if s.avg_match_score is not None:
                agg["match_scores"].append(s.avg_match_score)
            agg["weekly"].append({
                "date": d,
                "scraped": s.scraped,
                "shortlisted": s.shortlisted,
                "avg_match_score": s.avg_match_score,
            })

    results = []
    # Determine slope normalization scale: use interquartile range of slopes to avoid outliers
    slopes: List[float] = []
    for firm, agg in per_firm.items():
        ys = [w["avg_match_score"] for w in agg["weekly"] if w["avg_match_score"] is not None]
        if len(ys) >= 2:
            xs = list(range(len(ys)))
            slopes.append(_slope(xs, ys))
    slope_scale = (sorted(slopes)[int(0.75 * len(slopes))] if slopes else 0.05) or 0.05

    for firm, agg in per_firm.items():
        scraped = agg["scraped"]
        shortlisted = agg["shortlisted"]
        avg_score = statistics.fmean(agg["match_scores"]) if agg["match_scores"] else None

        # EB shrinkage toward global mean
        if avg_score is not None:
            n = len(agg["match_scores"])  # number of weeks with scores
            shrunk = (avg_score * n + global_mean * prior_weight) / (n + prior_weight)
        else:
            shrunk = global_mean  # no data -> global mean

        yield_rate = (shortlisted / scraped) if scraped > 0 else 0.0

        # Trend slope based on available weeks with scores
        ys = [w["avg_match_score"] for w in agg["weekly"] if w["avg_match_score"] is not None]
        slope = _slope(list(range(len(ys))), ys) if len(ys) >= 2 else 0.0
        # Map slope to 0..100 around 0 using slope_scale
        trend_component = max(0.0, min(100.0, 50.0 + 50.0 * (slope / slope_scale)))

        quality = 100.0 * shrunk
        yield_pct = 100.0 * yield_rate
        composite = 0.6 * quality + 0.3 * yield_pct + 0.1 * trend_component

        results.append({
            "firm": firm,
            "scraped_total": scraped,
            "shortlisted_total": shortlisted,
            "avg_match_score": avg_score,
            "shrunk_quality": shrunk,
            "yield_rate": yield_rate,
            "trend_slope": slope,
            "trend_component": trend_component,
            "composite_score": composite,
        })

    # Rank descending by composite
    results.sort(key=lambda r: r["composite_score"], reverse=True)
    for i, r in enumerate(results, start=1):
        r["rank"] = i

    output = {
        "algo_version": "v1_simple_10w_eb_shrink_trend",
        "window_weeks": window_weeks,
        "global_mean_match_score": global_mean,
        "dates_used": window_dates,
        "firms": results,
    }
    return output


def main(argv: List[str]) -> int:
    parser = argparse.ArgumentParser(description="Compute firm rankings from archived weekly data.")
    parser.add_argument("--weeks", type=int, default=10, help="Number of recent weeks to use (default: 10)")
    parser.add_argument("--out", default=os.path.join(DATA_DIR, "firm_ranking.json"), help="Output JSON path")
    args = parser.parse_args(argv)

    try:
        result = compute_ranking(window_weeks=args.weeks)
        out_path = args.out
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        _log(f"Wrote ranking to {out_path}; firms={len(result['firms'])}")
        return 0
    except SystemExit as e:
        msg = str(e)
        if msg:
            _log(f"ERROR: {msg}")
            return 1
        raise
    except Exception as e:
        _log(f"UNEXPECTED ERROR: {e.__class__.__name__}: {e}")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main(sys.argv[1:]))

