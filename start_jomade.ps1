# Jomade MVP Application Launcher (PowerShell)
# This script uses the official run.ps1 launcher

param(
    [switch]$NoWait,
    [switch]$OpenBrowser
)

# Set console title
$Host.UI.RawUI.WindowTitle = "Jomade MVP - Executive Search Application"

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Starting Jomade MVP Application" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Get application directory and ensure we're in the right place
$AppDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Write-Host "Script Directory: $AppDir" -ForegroundColor Yellow

# Change to the application directory
Set-Location $AppDir
$CurrentDir = Get-Location
Write-Host "Current Directory: $CurrentDir" -ForegroundColor Yellow
Write-Host ""

# Check if the official run.ps1 exists
$RunScript = Join-Path $AppDir "run.ps1"
if (-not (Test-Path $RunScript)) {
    Write-Host "ERROR: run.ps1 not found" -ForegroundColor Red
    Write-Host "Expected path: $RunScript" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Please ensure this script is in the Jomade MVP root directory" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found official launcher: run.ps1" -ForegroundColor Green
Write-Host ""
Write-Host "This will start:" -ForegroundColor Cyan
Write-Host "  - Backend server: http://localhost:8000" -ForegroundColor White
Write-Host "  - Frontend server: http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "Starting the official Jomade launcher..." -ForegroundColor Green
Write-Host ""

# Execute the official run.ps1 script
try {
    & $RunScript
}
catch {
    Write-Host ""
    Write-Host "ERROR: Failed to start the application" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Application has finished." -ForegroundColor Green

Write-Host "✅ Python is available" -ForegroundColor Green
$pythonVersion = python --version
Write-Host $pythonVersion -ForegroundColor Gray
Write-Host ""

# Check if required files exist
$requiredFiles = @(
    "backend\app.py",
    "frontend\index.html"
)

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "ERROR: $file not found" -ForegroundColor Red
        Write-Host "Expected path: $(Join-Path $CurrentDir $file)" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Directory contents:" -ForegroundColor Cyan
        Get-ChildItem -Name | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
        Write-Host ""
        Write-Host "Please ensure this script is in the Jomade MVP root directory" -ForegroundColor Yellow
        Write-Host ""
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "✅ Required files found" -ForegroundColor Green
Write-Host ""

# Check if virtual environment exists and activate it
if (Test-Path "venv\Scripts\Activate.ps1") {
    Write-Host "🔧 Activating virtual environment..." -ForegroundColor Blue
    try {
        & "venv\Scripts\Activate.ps1"
        Write-Host "✅ Virtual environment activated" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  Failed to activate virtual environment, using system Python" -ForegroundColor Yellow
    }
}
elseif (Test-Path "venv\Scripts\activate.bat") {
    Write-Host "🔧 Activating virtual environment (batch)..." -ForegroundColor Blue
    cmd /c "venv\Scripts\activate.bat"
    Write-Host "✅ Virtual environment activated" -ForegroundColor Green
}
else {
    Write-Host "⚠️  No virtual environment found, using system Python" -ForegroundColor Yellow
}
Write-Host ""

# Install/check dependencies
if (Test-Path "requirements.txt") {
    Write-Host "🔧 Checking dependencies..." -ForegroundColor Blue
    try {
        pip install -r requirements.txt --quiet
        Write-Host "✅ Dependencies checked" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ ERROR: Failed to install dependencies" -ForegroundColor Red
        Write-Host "Please check your requirements.txt file" -ForegroundColor Yellow
        Write-Host ""
        Read-Host "Press Enter to exit"
        exit 1
    }
}
else {
    Write-Host "⚠️  No requirements.txt found, skipping dependency check" -ForegroundColor Yellow
}
Write-Host ""

# Create data directory if it doesn't exist
if (-not (Test-Path "data")) {
    Write-Host "📁 Creating data directory..." -ForegroundColor Blue
    New-Item -ItemType Directory -Path "data" | Out-Null
}

# Start the application
Write-Host "🚀 Starting Jomade MVP Backend Server..." -ForegroundColor Green
Write-Host ""
Write-Host "🌐 The application will be available at:" -ForegroundColor Cyan
Write-Host "   http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "💡 To stop the application, press Ctrl+C in this window" -ForegroundColor Yellow
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Open browser if requested
if ($OpenBrowser) {
    Start-Sleep -Seconds 3
    Start-Process "http://localhost:3000"
}

# Start the Python backend
try {
    if ($NoWait) {
        Start-Process -FilePath "python" -ArgumentList "backend\app.py" -NoNewWindow
    }
    else {
        python backend\app.py
    }
}
catch {
    Write-Host ""
    Write-Host "❌ ERROR: Failed to start the application" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# If we get here, the application has stopped
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🛑 Jomade MVP Application has stopped" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

if (-not $NoWait) {
    Read-Host "Press Enter to exit"
}
