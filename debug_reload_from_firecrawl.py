#!/usr/bin/env python3
"""
Debug script to investigate why "Reload from Firecrawl" is not working for specific URLs.
This script will test the exact same logic used by the redownload endpoint.
"""

import os
import json
import sys
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the backend directory to the Python path
sys.path.append('backend')

def debug_reload_functionality():
    """Debug the reload from Firecrawl functionality for specific URLs."""
    
    print("🔍 DEBUG: Reload from Firecrawl Functionality")
    print("=" * 60)
    
    # Test URLs that are showing 0 jobs but have Firecrawl cache
    test_urls = [
        {
            "url": "https://www.hapeko.de/bewerbende/stellenangebote/p1",
            "prefix": "AAA",
            "name": "hapeko"
        },
        {
            "url": "https://www.psp.de/stellenausschreibungen.html", 
            "prefix": "AAD",
            "name": "psp"
        }
    ]
    
    # Check if Firecrawl API key is available
    firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
    if not firecrawl_api_key:
        print("❌ FIRECRAWL_API_KEY not found in environment")
        return
    
    print(f"✅ Firecrawl API key found: {firecrawl_api_key[:8]}...")
    
    # Check cache file
    cache_file = 'data/firecrawl_cache.json'
    if not os.path.exists(cache_file):
        print(f"❌ Cache file {cache_file} not found")
        return
    
    print(f"✅ Cache file found: {cache_file}")
    
    # Load cache data
    with open(cache_file, 'r', encoding='utf-8') as f:
        cache_data = json.load(f)
    
    print(f"✅ Cache loaded with {len(cache_data)} entries")
    
    # Import scraper
    try:
        from scraper import create_scraper
        scraper = create_scraper(timeout_seconds=300, cache_hours=24)
        if not scraper:
            print("❌ Failed to create scraper")
            return
        print("✅ Scraper created successfully")
    except Exception as e:
        print(f"❌ Error creating scraper: {e}")
        return
    
    # Test each URL
    for url_data in test_urls:
        url = url_data['url']
        prefix = url_data['prefix']
        name = url_data['name']
        
        print(f"\n🔍 Testing {name} ({prefix}): {url}")
        print("-" * 50)
        
        # Check if URL is in cache
        if url not in cache_data:
            print(f"❌ URL not found in cache")
            continue
        
        crawl_info = cache_data[url]
        crawl_id = crawl_info.get('crawl_id')
        timestamp = crawl_info.get('timestamp')
        
        print(f"✅ Found in cache:")
        print(f"   🆔 Crawl ID: {crawl_id}")
        print(f"   📅 Timestamp: {timestamp}")
        
        # Test the download_cached method directly
        print(f"\n🔄 Testing download_cached method...")
        try:
            jobs = scraper.download_cached(url, prefix, fetch_details=False)
            print(f"📊 download_cached returned: {len(jobs)} jobs")
            
            if jobs:
                print("✅ Jobs found:")
                for i, job in enumerate(jobs[:3]):  # Show first 3 jobs
                    print(f"   {i+1}. {job.get('id', 'N/A')}: {job.get('title', 'N/A')}")
                    print(f"      Company: {job.get('company', 'N/A')}")
                    print(f"      Location: {job.get('location', 'N/A')}")
            else:
                print("❌ No jobs extracted")
                
                # Let's check the Firecrawl status directly
                print(f"\n🔍 Checking Firecrawl status directly...")
                try:
                    status = scraper.firecrawl.check_crawl_status(crawl_id)
                    if status:
                        print(f"   📊 Status: {status.status}")
                        print(f"   📄 Data available: {bool(status.data)}")
                        if status.data:
                            print(f"   📄 Number of pages: {len(status.data)}")
                            
                            # Check first page content
                            if len(status.data) > 0:
                                first_page = status.data[0]
                                print(f"\n📄 First page analysis:")
                                
                                if isinstance(first_page, dict):
                                    markdown = first_page.get('markdown', '')
                                    metadata = first_page.get('metadata', {})
                                elif hasattr(first_page, 'markdown'):
                                    markdown = getattr(first_page, 'markdown', '')
                                    metadata = getattr(first_page, 'metadata', {})
                                else:
                                    markdown = ''
                                    metadata = {}
                                
                                print(f"   📝 Markdown length: {len(markdown)} chars")
                                print(f"   🔗 Source URL: {metadata.get('sourceURL', 'N/A') if isinstance(metadata, dict) else getattr(metadata, 'sourceURL', 'N/A')}")
                                
                                if markdown:
                                    print(f"   📝 Content preview (first 500 chars):")
                                    print(f"   {markdown[:500]}...")
                                    
                                    # Test LLM extraction directly
                                    print(f"\n🤖 Testing LLM extraction...")
                                    try:
                                        extracted_jobs = scraper._extract_jobs_with_llm(
                                            markdown, url, prefix, 1, fetch_details=False
                                        )
                                        print(f"   📊 LLM extracted: {len(extracted_jobs)} jobs")
                                        if extracted_jobs:
                                            for job in extracted_jobs[:2]:
                                                print(f"      - {job.get('title', 'N/A')} at {job.get('company', 'N/A')}")
                                    except Exception as e:
                                        print(f"   ❌ LLM extraction failed: {e}")
                                else:
                                    print(f"   ❌ No markdown content found")
                        else:
                            print(f"   ❌ No data in crawl results")
                    else:
                        print(f"   ❌ Failed to get crawl status")
                except Exception as e:
                    print(f"   ❌ Error checking Firecrawl status: {e}")
                    
        except Exception as e:
            print(f"❌ Error in download_cached: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_reload_functionality()
