#!/usr/bin/env python3
"""
Quick fix to extract and save bollmann-executives jobs.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend to path
sys.path.append('backend')

def quick_fix():
    """Quick fix for bollmann-executives."""
    
    print("🔧 QUICK FIX: bollmann-executives")
    
    try:
        from scraper import JobScraper
        from storage import JobStore
        
        # Create instances
        scraper = JobScraper()
        storage = JobStore('data/jobs.json')
        
        url = "https://www.bollmann-executives.de/ihre-karriere/stellenangebote"
        prefix = "AAW"
        
        print(f"📊 Current jobs in database: {len(storage.data)}")
        
        # Check current AAW jobs
        current_aaw = [job for job in storage.data if job.get('source') == 'AAW']
        print(f"📊 Current AAW jobs: {len(current_aaw)}")
        
        # Run scraper
        print(f"🔍 Running scraper for {url}...")
        jobs = scraper.crawl_url(url, prefix, force_scrape=False)
        
        print(f"📊 Extracted: {len(jobs)} jobs")
        
        if jobs:
            # Save jobs
            for job in jobs:
                storage.add(job)
            
            # Check final count
            final_aaw = [job for job in storage.data if job.get('source') == 'AAW']
            print(f"✅ Final AAW jobs: {len(final_aaw)}")
            print(f"📈 Added: {len(final_aaw) - len(current_aaw)} jobs")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_fix()
