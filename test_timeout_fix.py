#!/usr/bin/env python3
"""
Test the timeout fix for the race condition issue.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend to path
sys.path.append('backend')

def test_timeout_fix():
    """Test the increased timeout values."""
    
    print("=" * 80)
    print("🧪 TESTING TIMEOUT FIX FOR RACE CONDITION")
    print("=" * 80)
    
    try:
        from scraper import JobScraper, create_scraper
        
        # Test default timeout values
        print("🔧 Testing default timeout values...")
        
        # Test direct instantiation
        scraper1 = JobScraper()
        print(f"✅ Direct JobScraper() timeout: {scraper1.timeout_seconds}s (should be 900s = 15 minutes)")
        
        # Test factory function
        scraper2 = create_scraper()
        print(f"✅ create_scraper() timeout: {scraper2.timeout_seconds}s (should be 900s = 15 minutes)")
        
        # Test custom timeout
        scraper3 = create_scraper(timeout_seconds=1200)  # 20 minutes
        print(f"✅ create_scraper(1200) timeout: {scraper3.timeout_seconds}s (should be 1200s = 20 minutes)")
        
        print(f"\n📊 TIMEOUT COMPARISON:")
        print(f"   Old default: 300s (5 minutes) → New default: 900s (15 minutes)")
        print(f"   Old factory: 180s (3 minutes) → New factory: 900s (15 minutes)")
        print(f"   Improvement: 3x longer timeout should prevent race conditions")
        
        # Test with a URL that previously had timeout issues
        print(f"\n🧪 Testing delphi-hr.de (previously missing jobs)...")
        
        url = "https://delphi-hr.de/jobs/"
        prefix = "AAZ"
        
        print(f"📍 URL: {url}")
        print(f"🔧 Timeout: {scraper2.timeout_seconds}s")
        print(f"🚀 Starting crawl (this may take up to 15 minutes)...")
        
        # This should now work without timing out
        jobs = scraper2.crawl_url(url, prefix, force_scrape=False)
        
        print(f"📊 Result: {len(jobs)} jobs extracted")
        
        if jobs:
            print(f"✅ SUCCESS: Timeout fix worked! Jobs extracted:")
            for job in jobs[:3]:  # Show first 3
                print(f"   - {job['id']}: {job['title'][:50]}...")
        else:
            print(f"⚠️ No jobs extracted - may need to check Firecrawl dashboard")
            print(f"   If crawl is still running, wait and try again later")
        
    except Exception as e:
        print(f"❌ Error testing timeout fix: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "=" * 80)
    print("🏁 TIMEOUT FIX TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_timeout_fix()
